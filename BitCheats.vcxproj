﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>16.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{35435125-455b-45ca-9b0e-2068d268d7e0}</ProjectGuid>
    <RootNamespace>BitCheats</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v142</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props" />
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <TargetName>BitCheats</TargetName>
    <IncludePath>$(IncludePath);$(SolutionDir)BitCheats\Menu UI\ImGui</IncludePath>
    <IntDir>$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_CONSOLE;IMGUI_DEFINE_MATH_OPERATORS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level1</WarningLevel>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <IntrinsicFunctions>false</IntrinsicFunctions>
      <SDLCheck>
      </SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_CONSOLE;IMGUI_DEFINE_MATH_OPERATORS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Full</Optimization>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <AdditionalOptions>/bigobj /utf-8 %(AdditionalOptions)</AdditionalOptions>
      <LanguageStandard_C>Default</LanguageStandard_C>
      <OmitFramePointers>false</OmitFramePointers>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <ExceptionHandling>false</ExceptionHandling>
      <ProgramDataBaseFileName />
    </ClCompile>
    <Link>
      <SubSystem>Console</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(DXSDK_DIR)Lib\x64</AdditionalLibraryDirectories>
      <AdditionalOptions>/bigobj /utf-8 %(AdditionalOptions)</AdditionalOptions>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ProgramDatabaseFile>$(OutDir)$(TargetName).pdb</ProgramDatabaseFile>
      <AdditionalDependencies>urlmon.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <!-- Menu UI files -->
    <ClCompile Include="Menu UI\ImGui\stb_image_impl.cpp" />
    <ClCompile Include="Menu UI\HImGuiImageManager\HImGuiImageManager.cpp" />
    <ClCompile Include="Cheat Core\Features\Aimbot\Aimbot.cpp" />
    <ClCompile Include="Cheat Core\Features\Caching\Cache.cpp" />
    <ClCompile Include="Cheat Core\Features\Config\Config.cpp" />
    <ClCompile Include="Cheat Core\Features\FontSystem\FontSystem.cpp" />
    <ClCompile Include="Cheat Core\Features\Loot\icons.cpp" />
    <ClCompile Include="Cheat Core\Features\Loot\Loot.cpp" />
    <ClCompile Include="Cheat Core\Features\Visuals\Drawing\Drawing.cpp" />
    <ClCompile Include="Cheat Core\Features\Visuals\PlayerVisuals.cpp" />
    <ClCompile Include="Cheat Core\Kernel Driver\Include\PortableExecutable\PortableExecutable.cpp" />
    <ClCompile Include="Cheat Core\Settings\Settings.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\AbstractAnimation.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\AnimationGroup.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\Easing.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\EasingCurve.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ImVec2Anim.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ImVec4Anim.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\ParallelAnimationGroup.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\PauseAnimation.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\SequentialAnimationGroup.cpp" />
    <ClCompile Include="Menu UI\Components\Animation\ImAnim\Utils.cpp" />
    <ClCompile Include="Menu UI\Components\Components.cpp" />
    <ClCompile Include="Menu UI\Components\nav_elements.cpp" />
    <ClCompile Include="Menu UI\Framwork\AnimationSystem.cpp" />
    <ClCompile Include="Menu UI\Framwork\GUI.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_demo.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_draw.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_impl_dx11.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_impl_win32.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_tables.cpp" />
    <ClCompile Include="Menu UI\ImGui\imgui_widgets.cpp" />
    <!-- Render files -->
    <ClCompile Include="Render\Cheat Menu\CheatMenu.cpp" />
    <!-- Cheat Core files -->
    <!-- Kernel Driver files -->
    <!-- Utils files -->
    <ClCompile Include="Utils\SignatureScanner.cpp" />
    <!-- Main file -->
    <ClCompile Include="main.cpp" />
  </ItemGroup>
  <ItemGroup>
    <!-- Menu UI files -->
    <ClInclude Include="Menu UI\HImGuiImageManager\HImGuiImageManager.h" />
    <ClInclude Include="Cheat Core\Features\Aimbot\Aimbot.h" />
    <ClInclude Include="Cheat Core\Features\Caching\Cache.h" />
    <ClInclude Include="Cheat Core\Features\Config\Config.h" />
    <ClInclude Include="Cheat Core\Features\FontSystem\FontSystem.h" />
    <ClInclude Include="Cheat Core\Features\Input\HotkeySystem.h" />
    <ClInclude Include="Cheat Core\Features\Loot\icons.h" />
    <ClInclude Include="Cheat Core\Features\Loot\Loot.h" />
    <ClInclude Include="Cheat Core\Features\Visuals\Drawing\Drawing.h" />
    <ClInclude Include="Cheat Core\Features\Visuals\PlayerVisuals.h" />
    <ClInclude Include="Cheat Core\Framwork\HexArray.h" />
    <ClInclude Include="Cheat Core\Framwork\Math.h" />
    <ClInclude Include="Cheat Core\Framwork\Vectors.h" />
    <ClInclude Include="Cheat Core\GameClass\GameFunctions.h" />
    <ClInclude Include="Cheat Core\GameClass\GameSettings.h" />
    <ClInclude Include="Cheat Core\GameClass\Offsets.h" />
    <ClInclude Include="Cheat Core\Kernel Driver\Driver\Driver.h" />
    <ClInclude Include="Cheat Core\Kernel Driver\Include\PortableExecutable\PortableExecutable.h" />
    <ClInclude Include="Cheat Core\Kernel Driver\Include\utils.h" />
    <ClInclude Include="Cheat Core\Kernel Driver\Include\Variadicstring.h" />
    <ClInclude Include="Cheat Core\Settings\Settings.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\AbstractAnimation.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\AnimationGroup.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\custom_functions.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\Easing.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\EasingCurve.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ImVec2Anim.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ImVec4Anim.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\ParallelAnimationGroup.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\PauseAnimation.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\SequentialAnimationGroup.h" />
    <ClInclude Include="Menu UI\Components\Animation\ImAnim\Utils.h" />
    <ClInclude Include="Menu UI\Components\Components.h" />
    <ClInclude Include="Menu UI\Components\nav_elements.h" />
    <ClInclude Include="Menu UI\Fonts\font_defines.h" />
    <ClInclude Include="Menu UI\Fonts\icons.h" />
    <ClInclude Include="Menu UI\Framwork\AnimationSystem.h" />
    <ClInclude Include="Menu UI\Framwork\GUI.h" />
    <ClInclude Include="Menu UI\Framwork\GUI_Helper.h" />
    <ClInclude Include="Menu UI\HImGuiImageManager\Logger.h" />
    <ClInclude Include="Menu UI\ImGui\blur\blur.hpp" />
    <ClInclude Include="Menu UI\ImGui\imconfig.h" />
    <ClInclude Include="Menu UI\ImGui\imgui.h" />
    <ClInclude Include="Menu UI\ImGui\imgui_impl_dx11.h" />
    <ClInclude Include="Menu UI\ImGui\imgui_impl_win32.h" />
    <ClInclude Include="Menu UI\ImGui\imgui_internal.h" />
    <ClInclude Include="Menu UI\ImGui\imgui_settings.h" />
    <ClInclude Include="Menu UI\ImGui\imstb_rectpack.h" />
    <ClInclude Include="Menu UI\ImGui\imstb_textedit.h" />
    <ClInclude Include="Menu UI\ImGui\imstb_truetype.h" />
    <ClInclude Include="Menu UI\ImGui\stb_image.h" />
    <!-- Render files -->
    <ClInclude Include="Render\Cheat Menu\CheatMenu.h" />
    <ClInclude Include="Utils.h" />
    <ClInclude Include="Utils\SignatureScanner.h" />
    <!-- Cheat Core files -->
    <!-- Kernel Driver files -->
    <!-- SDK files -->
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
    <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets" />
  </ImportGroup>
</Project>