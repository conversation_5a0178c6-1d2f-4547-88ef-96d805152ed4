#define IMGUI_DEFINE_MATH_OPERATORS
#include "GUI.h"
#include "../Components/Components.h"
#include <functional>
#include "../../Utils.h"
#include "../Components/nav_elements.h"
#include "../Fonts/icons.h"
#include "../Fonts/font_defines.h"
#include "../Fonts/custom_icons.h"
#include <iostream>
#include "../../Cheat Core/Features/Config/Config.h"
#include "../../Cheat Core/Settings/Settings.h"
#include "../../Cheat Core/Features/FontSystem/FontSystem.h"
#include <algorithm>  // for std::transform
#include <string.h>    // for strstr
#include <sstream>     // for std::istringstream
#include <vector>      // for std::vector
#include <cmath>       // for sin/cos/pow functions
#include <cstdlib>     // for rand()
#include "GUI_Helper.h"
#include "../Blur/blur.hpp"
#define STBI_ONLY_GIF
#include "../../Menu UI/ImGui/stb_image.h"
#include "../../Menu UI/HImGuiImageManager/HImGuiImageManager.h"
#include <chrono>

// External DirectX resources from main.cpp
extern IDXGISwapChain* g_pSwapChain;

CGui pGUi = CGui();

// Global frame marker for config popup open guarding
static int g_configPopupOpenFrame = -1;

// Localization helpers for title + description + icon
static inline std::string Lbl(const char* key) { return GET_TEXT(key); }
static inline std::string Desc(const char* key, const char* icon) {
    std::string dKey = std::string(key) + ".desc";
    std::string text = GET_TEXT(dKey);
    // Icon is now passed as a separate parameter to nav_elements functions
    return text;
}

float accent_color[4] = { 0.f / 255.f, 145.f / 255.f, 255.f / 255.f, 1.00f };
float accent_color_alpha[4] = { 0.f / 255.f, 145.f / 255.f, 255.f / 255.f, 1.00f };

// Animation helper methods
float CGui::EaseOutExpo(float x) {
    return x == 1.0f ? 1.0f : 1.0f - powf(2.0f, -10.0f * x);
}

float CGui::EaseInOutQuad(float x) {
    return x < 0.5f ? 2.0f * x * x : 1.0f - powf(-2.0f * x + 2.0f, 2.0f) / 2.0f;
}

// Gradient text rendering helper - relative to current window
void CGui::RenderGradientText(const char* text, ImVec2 pos, ImColor startColor, ImColor endColor, ImFont* font) {
    // Use current window draw list instead of foreground
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Get current cursor position if pos is zero
    ImVec2 cursorPos = ImGui::GetCursorPos();
    if (pos.x == 0 && pos.y == 0) {
        pos = cursorPos;
    }

    // Calculate text size for gradient end position
    ImVec2 textSize = ImGui::CalcTextSize(text);

    // Set the cursor position
    ImGui::SetCursorPos(pos);

    // Get the absolute position for drawing
    ImVec2 textPos = ImGui::GetCursorScreenPos();

    // Horizontally center the text in window
    ImVec2 windowSize = ImGui::GetWindowSize();
    textPos.x = textPos.x + (windowSize.x - textSize.x) * 0.5f - ImGui::GetScrollX();

    // Push font if provided
    if (font) {
        ImGui::PushFont(font);
    }

    // Get vertex buffer index before drawing text
    const int vtx_idx_start = drawList->VtxBuffer.Size;

    // Draw the text (this adds vertices to the buffer)
    drawList->AddText(textPos, ImColor(1.0f, 1.0f, 1.0f, 1.0f), text);

    // Get vertex buffer index after drawing text
    const int vtx_idx_end = drawList->VtxBuffer.Size;

    // Apply gradient to the text vertices
    ImGui::ShadeVertsLinearColorGradientSetAlpha(
        drawList,
        vtx_idx_start,
        vtx_idx_end,
        textPos,
        ImVec2(textPos.x + textSize.x, textPos.y),
        startColor,
        endColor
    );

    // Pop font if pushed
    if (font) {
        ImGui::PopFont();
    }

    // Add spacing after text - advance cursor
    //ImGui::SetCursorPosY(pos.y + textSize.y);

    // Ensure ImGui knows this space was used (for layout)
    ImGui::Dummy(ImVec2(textSize.x, textSize.y));
}

// Gradient rectangle drawing helper for current window
void CGui::DrawGradientRect(ImRect rect, ImU32 color1, ImU32 color2, float thickness) {
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Left part of the gradient
    const int vtx_idx_1 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1);
    drawList->AddShadowRect(rect.Min, ImVec2(rect.GetCenter().x - 5.5f, rect.Max.y), color1, thickness, ImVec2(0, 0));
    const int vtx_idx_2 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_1, vtx_idx_2, rect.Min, ImVec2(rect.GetCenter().x, rect.Max.y), color1, color2);

    // Right part of the gradient
    const int vtx_idx_3 = drawList->VtxBuffer.Size;
    drawList->AddRectFilled(ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color1);
    drawList->AddShadowRect(ImVec2(rect.GetCenter().x + 5.5f, rect.Min.y), rect.Max, color1, thickness, ImVec2(0, 0));
    const int vtx_idx_4 = drawList->VtxBuffer.Size;
    ImGui::ShadeVertsLinearColorGradientSetAlpha(drawList, vtx_idx_3, vtx_idx_4, ImVec2(rect.GetCenter().x, rect.Min.y), rect.Max, color2, color1);
}

// Render gradient separator line - relative to current window
void CGui::RenderGradientSeparator(float y_offset, float width_percentage = 0.75f) {
    // Get current window draw list
    ImDrawList* drawList = ImGui::GetWindowDrawList();

    // Save current cursor position
    ImVec2 cursorPos = ImGui::GetCursorPos();

    // Calculate separator position
    ImVec2 windowSize = ImGui::GetWindowSize();
    float line_height = 2.0f; // Height of the separator line

    // Calculate the width based on percentage of window width
    float line_width = (windowSize.x - ImGui::GetStyle().WindowPadding.x * 2) * width_percentage;

    // Set cursor position for the separator
    ImGui::SetCursorPosY(cursorPos.y + y_offset);

    // Get screen position for drawing
    ImVec2 lineStart = ImGui::GetCursorScreenPos();

    // Calculate center position for alignment
    float remaining_space = windowSize.x - ImGui::GetStyle().WindowPadding.x * 2 - line_width;
    float start_x_offset = remaining_space / 2.0f; // Center the line

    // Adjust lineStart to center the line
    lineStart.x += start_x_offset;

    // Draw the main filled rect for the separator line
    drawList->AddRectFilled(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f))
    );

    // Create a rectangle for the gradient effect
    ImRect gradient_rect(
        lineStart,
        ImVec2(lineStart.x + line_width, lineStart.y + line_height)
    );

    // Apply the gradient effect
    DrawGradientRect(
        gradient_rect,
        ImColor(1.0f, 1.0f, 1.0f, 0.1f),
        ImGui::ColorConvertFloat4ToU32(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f)),
        35.0f
    );

    // Restore cursor position with an offset for spacing
    ImGui::SetCursorPosY(cursorPos.y + y_offset + line_height + 5.0f);
}

// Helper to render a section header that scrolls with the content
void CGui::RenderSectionHeader(const char* title, float titleY, float separatorY) {
    // Save cursor position for ImGui layout
    ImVec2 startCursorPos = ImGui::GetCursorPos();

    // Set cursor position for the title if specified
    if (titleY > 0) {
        ImGui::SetCursorPosY(titleY);
    }

    // Render the title with gradient
    RenderGradientText(
        title,
        ImVec2(0, 0), // Use current cursor position
        ImColor(accent_color[2], accent_color[1], accent_color[0], 1.0f),
        ImColor(1.0f, 1.0f, 1.0f, 1.0f)
    );

    // Render the separator line
    RenderGradientSeparator(0, 0.75f); // Use current cursor position

    // Add spacing after the section header
    ImGui::Dummy(ImVec2(0, 8));
}

void CGui::Initialize(ID3D11Device* g_pd3dDevice) {
    ImGuiIO& io = ImGui::GetIO();
    (void)io;
    ImFontConfig fontConfig;
    fontConfig.MergeMode = true;
    fontConfig.PixelSnapH = true;
    static ImFontConfig icons_config;
    icons_config.OversampleH = icons_config.OversampleV = 1;
    icons_config.MergeMode = true;
    icons_config.GlyphOffset.y = 6.5f;

    static ImFontConfig icomoon_config2;
    icomoon_config2.OversampleH = icomoon_config2.OversampleV = 1;
    icomoon_config2.MergeMode = true;
    icomoon_config2.GlyphOffset.y = 6.5f;

    auto fontPath = LoadChinese();
    std::string savepath = "C:\\BitCheats\\IconsFont.ttf";
    io.Fonts->AddFontFromMemoryTTF(&normal_font, sizeof(normal_font), 20, NULL, io.Fonts->GetGlyphRangesCyrillic());
    if (!fontPath.empty()) {
        io.Fonts->AddFontFromFileTTF(fontPath.c_str(), 20, &fontConfig, nav_elements::GetGlyphRangesChinese());
    }
    io.AddInputCharacter(0x410);
    io.AddInputCharacter(0x4E00);
    static ImWchar icomoon_ranges[] = { 0x1, 0x10FFFD, 0 };

    static const ImWchar icons_ranges[] = { ICON_MIN_MS, ICON_MAX_16_MS, 0 };


    ImFontConfig small_cfg = icons_config;
    small_cfg.MergeMode = false;             // ← this is the key
    small_cfg.OversampleH = icons_config.OversampleV = 1;
    small_cfg.GlyphOffset.y = 6.5f;

    float baseSize = 55.0f * 1.3f / 2.0f;   // ≈ 35.75px
    float smallSize = 45.0f * 1.3f / 2.0f;   // ≈ 35.75px

    // big icons (merged into your default font)
    iconsBig = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), baseSize, &icons_config, icons_ranges);

    // small icons (stand-alone, separate atlas entry)
    iconsSmall = io.Fonts->AddFontFromFileTTF(
        savepath.c_str(), smallSize, &small_cfg, icons_ranges);

    // Initialize the font system
    g_FontSystem.Initialize();

    nav_elements::Theme();

    // Initialize states
    isMenuOpen = false;
    lastTabChangeTime = ImGui::GetTime();

    // Initialize the animation system
    InitializeAnimations();

    // Initialize the configuration system
    InitializeConfigSystem();
}

void CGui::Render(bool& ShowMenu) {
    // Update animation system
    UpdateAnimations();
    
    // Animated menu state tracking
    if (ShowMenu != isMenuOpen) {
        isMenuOpen = ShowMenu;
        lastMenuToggleTime = ImGui::GetTime();
        
        if (isMenuOpen) {
            // Take snapshot of current settings when menu is opened
            g_ConfigSnapshot.CaptureSettings();
            // Clear notifications when menu is opened to fix the bug
            g_NotificationManager.ClearChanges();
            // Start show animation
            StartMenuShowAnimation();
        } else {
            // Start hide animation
            StartMenuHideAnimation();
        }
    }

    // Always render profile window first
    this->RenderProfileWindow();
    
    // g_NotificationManager.ShowNotifications(); // Disabled for cleaner UI without animated lines

    // Get window animation state for both header and main menu
    auto& earlyWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
    
    // Render header window with same visibility logic as main menu 
    if (ShowMenu || earlyWindowState.isVisible || earlyWindowState.isAnimating) {
        this->RenderHeaderWindow();
    }
    
    // Skip rendering main window if not visible and not animating
    if (!ShowMenu && !earlyWindowState.isVisible && !earlyWindowState.isAnimating) {
        return;
    }


    // Handle tab switching with animations
    float currentTime = ImGui::GetTime();
    if (currentTab != previousTab) {
        StartTabSwitchAnimation(previousTab, currentTab);
        previousTab = currentTab;
        lastTabChangeTime = currentTime;
        featureStartTime = currentTime;
    }

    // Simple fade animation without scaling or overlays
    ImGui::SetNextWindowSize(ImVec2(1020, 635));
    // Only apply simple alpha fade, no scaling
    auto& mainBgState = AnimationSystem::g_AnimationManager.GetWindowState();
    if (mainBgState.isAnimating) {
        ImGui::SetNextWindowBgAlpha(mainBgState.alpha);
    } else {
        // Default transparency for the main menu window
        ImGui::SetNextWindowBgAlpha(0.95f);
    }


    if (!BeginAnimatedWindow("##MainWindow", &ShowMenu, ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoSavedSettings | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoScrollWithMouse)) {
        EndAnimatedWindow();
        return;
    }
    {
        // Apply content alpha animation only within this window context
        auto& mainWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (mainWindowState.alpha < 1.0f) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, mainWindowState.alpha);
        }

        gui.window_pos = ImGui::GetWindowPos();
        gui.window_size = ImGui::GetWindowSize();

        auto draw = ImGui::GetWindowDrawList();
        const auto& p = ImGui::GetWindowPos();
        ImGuiStyle& style = ImGui::GetStyle();

        // Navigation tabs moved to top
        ImGui::SetCursorPos(ImVec2(10, 10));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(81 / 255.0f, 65 / 255.0f, 55 / 255.0f, 0.5f));
        // Use exact same background color as main window
        ImVec4 tabsBgColor = ImGui::GetStyleColorVec4(ImGuiCol_WindowBg);
        auto& tabsBgState = AnimationSystem::g_AnimationManager.GetWindowState();
        tabsBgColor.w = tabsBgState.isAnimating ? tabsBgState.alpha : 0.95f;

        ImGui::PushStyleColor(ImGuiCol_ChildBg, tabsBgColor);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.5f);
        ImGui::BeginChild("Tabs", ImVec2(200, 615), ImGuiChildFlags_Border, ImGuiWindowFlags_NoScrollbar);
        {
            ImGui::SetCursorPos(ImVec2(0, 50));
            if (nav_elements::Tab(GET_TEXT("aimbot.title").c_str(), ICON_MS_TARGET, &currentTab, ModernUI::NavigationPanel::TAB_AIMBOT)) {}
            if (nav_elements::Tab(GET_TEXT("esp.players.title").c_str(), ICON_MS_VISIBILITY, &currentTab, ModernUI::NavigationPanel::TAB_VISUALS)) {}
            if (nav_elements::Tab(GET_TEXT("esp.items.title").c_str(), ICON_MS_INVENTORY, &currentTab, ModernUI::NavigationPanel::TAB_ITEMS)) {}
            if (nav_elements::Tab(GET_TEXT("radar.title").c_str(), ICON_MS_RADAR, &currentTab, ModernUI::NavigationPanel::TAB_RADAR)) {}
            if (nav_elements::Tab("Settings", ICON_MS_SETTINGS, &currentTab, ModernUI::NavigationPanel::TAB_SETTINGS)) {}
            if (nav_elements::Tab(GET_TEXT("config.title").c_str(), ICON_MS_FOLDER, &currentTab, ModernUI::NavigationPanel::TAB_CONFIGS)) {}
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // Pop border size
        ImGui::PopStyleColor(2); // Pop border color and main window background color for navigation tabs

        // Content area with animations
        ImGui::SetCursorPos(ImVec2(220, 10));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(81 / 255.0f, 65 / 255.0f, 55 / 255.0f, 0.5f));
        // Use exact same background color as main window
        ImVec4 contentBgColor = ImGui::GetStyleColorVec4(ImGuiCol_WindowBg);
        auto& contentBgState = AnimationSystem::g_AnimationManager.GetWindowState();
        contentBgColor.w = contentBgState.isAnimating ? contentBgState.alpha : 0.95f;

        ImGui::PushStyleColor(ImGuiCol_ChildBg, contentBgColor);
        ImGui::PushStyleVar(ImGuiStyleVar_ChildBorderSize, 0.5f);
        
        // Create unique ID for each tab's content area to maintain separate scroll positions
        std::string contentAreaId = "ContentArea_";
        switch (currentTab) {
            case ModernUI::NavigationPanel::TAB_AIMBOT:
                contentAreaId += "Aimbot";
                break;
            case ModernUI::NavigationPanel::TAB_VISUALS:
                contentAreaId += "Visuals";
                break;
            case ModernUI::NavigationPanel::TAB_ITEMS:
                contentAreaId += "Items";
                break;
            case ModernUI::NavigationPanel::TAB_RADAR:
                contentAreaId += "Radar";
                break;
            case ModernUI::NavigationPanel::TAB_SETTINGS:
                contentAreaId += "Settings";
                break;
            case ModernUI::NavigationPanel::TAB_CONFIGS:
                contentAreaId += "Configs";
                break;
            default:
                contentAreaId += "Default";
                break;
        }
        
        ImGui::BeginChild(contentAreaId.c_str(), ImVec2(790, 615), ImGuiChildFlags_Border);
        {
            BeginAnimatedContent();
            
            ImGui::Indent(5.0f);
            
            // Animated page title and description
            RenderAnimatedElement(0, [this]() {
                ImGui::Dummy(ImVec2(0, 15));
                
                // Get tab name and description
                const char* tabName = GetTabName(currentTab);
                const char* tabDesc = GetTabDescription(currentTab);
                
                // Title with larger font and accent color
                ImGui::PushFont(iconsBig);
                ImGui::TextColored(ImVec4(accent_color[2], accent_color[1], accent_color[0], 1.0f), "%s", tabName);
                ImGui::PopFont();
                
                // Description on same line with spacing
                ImGui::SameLine();
                ImGui::Dummy(ImVec2(20, 0)); // Add some spacing
                ImGui::SameLine();
                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "- %s", tabDesc);
                
                ImGui::Dummy(ImVec2(0, 10));
            });
            
            // Render tab content with smooth transitions
            auto& tabState = AnimationSystem::g_AnimationManager.GetTabState();            
            // Render current tab content with animations
            RenderAnimatedElement(1, [this]() {
                switch (currentTab) {
                    case ModernUI::NavigationPanel::TAB_AIMBOT:
                        RenderAimbotTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_VISUALS:
                        RenderVisualsTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_ITEMS:
                        RenderMainTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_RADAR:
                        RenderRadarTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_SETTINGS:
                        RenderSettingsTab();
                        break;
                    case ModernUI::NavigationPanel::TAB_CONFIGS:
                        RenderConfigsTab();
                        break;
                }
            });

            ImGui::Unindent(5.0f);
            ImGui::Dummy(ImVec2(100, 10));
            
            EndAnimatedContent();
        }
        ImGui::EndChild();
        ImGui::PopStyleVar(); // Pop border size
        ImGui::PopStyleColor(2); // Pop border color and main window background color for content area
        
        // Pop content alpha animation if it was applied
        auto& mainContentState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (mainContentState.alpha < 1.0f) {
            ImGui::PopStyleVar(); // Pop content alpha
        }
    }
    EndAnimatedWindow();

    ImGuiContext& context = *GImGui;
    ImGuiStyle& style = ImGui::GetStyle();

    // Enhanced dark overlay system with proper click detection
    static float dark_overlay = 0.f;

    // Update dark overlay animation based on gui.darkoverlay flag
    dark_overlay = ImLerp(dark_overlay, gui.darkoverlay ? 0.75f : 0.f, GetAnimSpeed());

    // Handle dark overlay click detection
    if (dark_overlay > 0.01f) {
        // Reset click flag at start of each frame
        gui.darkoverlay_clicked = false;

        // Check for clicks on the dark overlay
        if (ImGui::IsMouseClicked(ImGuiMouseButton_Left)) {
            // Check if click is outside any popup window
            bool clickedOnPopup = false;
            for (ImGuiWindow* window : context.Windows) {
                if ((window->Flags & ImGuiWindowFlags_Popup) &&
                    ImGui::IsMouseHoveringRect(window->Pos, window->Pos + window->Size)) {
                    clickedOnPopup = true;
                    break;
                }
            }

            // If click was not on a popup, it's a dark overlay click
            if (!clickedOnPopup && !ImGui::IsAnyItemHovered() && !ImGui::IsAnyItemActive()) {
                gui.darkoverlay_clicked = true;
            }
        }

        // Apply dark overlay to all non-popup windows when darkoverlay is active
        for (ImGuiWindow* window : context.Windows) {
            // Skip popup windows and the combo window
            if (!(window->Flags & ImGuiWindowFlags_Popup) && window != gui.combo_window) {
                // Add dark overlay rectangle - render only within this specific window's bounds
                window->DrawList->AddRectFilled(gui.window_pos, gui.window_pos + gui.window_size,
                    func.GetColorWithAlpha(gui.window_bg, dark_overlay), style.WindowRounding, ImDrawFlags_RoundCornersAll);
            }
        }
    } else {
        // Reset click flag when overlay is not active
        gui.darkoverlay_clicked = false;
    }

    // Sync settings with global variables to apply changes in real-time
    SettingsHelper::SyncSettings();

    // Profile and header windows already rendered earlier - don't render again
    // g_NotificationManager.ShowNotifications(); // Disabled for cleaner UI without animated lines
    
    // Final safeguard: Reset any potential global alpha leaks from UI systems
    ImGuiContext* ctx = ImGui::GetCurrentContext();
    ctx->Style.Alpha = 1.0f; // Ensure global alpha is always reset to full opacity
    
    // Overlay state is now controlled exclusively by components/popups; no global detection here.
}

// Simple feature animation - no longer used for individual elements
bool CGui::AnimateNextFeature() {
    featureIndex++;
    return true; // Always render content immediately
}

// Removed animated water background for cleaner UI
void CGui::RenderIOSWaterAnimation(float progress, ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

// Simple slide-up animation for content elements
void CGui::AnimateContentElement(float delay) {
    const float baseDelay = 0.04f; // Stagger timing between elements
    const float animationDuration = 0.5f; // Animation duration
    
    float animationStartTime = featureStartTime + (featureIndex * baseDelay) + delay;
    float currentTime = ImGui::GetTime();
    float elapsed = currentTime - animationStartTime;

    // Only apply animation if we're in the animation timeframe
    if (elapsed >= 0.0f && elapsed <= animationDuration) {
        float progress = elapsed / animationDuration;
        
        // Simple slide up animation
        float easedProgress = EaseOutBack(progress);
        float slideOffset = (1.0f - easedProgress) * 25.0f;
        
        // Apply the slide offset
        ImVec2 currentPos = ImGui::GetCursorPos();
        ImGui::SetCursorPos(ImVec2(currentPos.x, currentPos.y + slideOffset));
    }

    featureIndex++;
}

// Helper function to get tab name
const char* CGui::GetTabName(int tabIndex) {
    static std::string s;
    switch (tabIndex) {
        case 0: s = GET_TEXT("aimbot.title"); break;
        case 1: s = GET_TEXT("esp.players.title"); break;
        case 2: s = GET_TEXT("esp.items.title"); break;
        case 3: s = GET_TEXT("radar.title"); break;
        case 4: s = "Settings"; break;
        case 5: s = GET_TEXT("config.title"); break;
        default: s = "Unknown"; break;
    }
    return s.c_str();
}

// Helper function to get tab description
const char* CGui::GetTabDescription(int tabIndex) {
    // Descriptions can be localized in the future; using concise neutral text
    switch (tabIndex) {
        case 0: return "";
        case 1: return "";
        case 2: return "";
        case 3: return "";
        case 4: return "";
        case 5: return "";
        default: return "";
    }
}

// iOS-style smooth easing functions
float CGui::EaseInOutCubic(float x) {
    return x < 0.5f ? 4.0f * x * x * x : 1.0f - powf(-2.0f * x + 2.0f, 3.0f) / 2.0f;
}

float CGui::EaseOutBack(float x) {
    const float c1 = 1.70158f;
    const float c3 = c1 + 1.0f;
    return 1.0f + c3 * powf(x - 1.0f, 3.0f) + c1 * powf(x - 1.0f, 2.0f);
}

// Removed animated tab transition for cleaner UI
void CGui::RenderTabTransition(float progress, ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

// Star data structure for glowing animation
struct StarData {
    ImVec2 position;
    ImVec2 velocity;
    float brightness;
    float brightnessPhase;
    float size;
    float sizePhase;
    ImVec4 color;
};

// Removed animated glowing stars for cleaner UI
void CGui::RenderGlowingStars(ImVec2 contentSize) {
    // Animation disabled for cleaner menu experience
    return;
}

void CGui::RenderAimbotTab() {
    // Render all content immediately - no individual delays
    // Main aimbot toggles
    auto& general = AIMBOT_SETTINGS.general;
    auto& perWeapon = AIMBOT_SETTINGS.perWeaponSettings;

    if (general.perWeapon) {
        int selectedWeapon = CONFIG_SETTINGS.selectedWeapon;
        AimbotCore& weaponSettings = SettingsManager::GetWeaponSetting(perWeapon, selectedWeapon, general);

        nav_elements::CheckboxComponent(Lbl("aimbot.enable").c_str(), &weaponSettings.enabled, Desc("aimbot.enable", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TARGET);
        nav_elements::CheckboxComponent(Lbl("aimbot.aim_lock").c_str(), &weaponSettings.aimLock, Desc("aimbot.aim_lock", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true, ICON_MS_LOCK);
        nav_elements::CheckboxComponent(Lbl("aimbot.predict").c_str(), &weaponSettings.predict, Desc("aimbot.predict", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TRENDING_UP);
        nav_elements::CheckboxComponent(Lbl("aimbot.save_target").c_str(), &weaponSettings.saveTarget, Desc("aimbot.save_target", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_SAVE);
        nav_elements::CheckboxComponent(Lbl("aimbot.visibility_check").c_str(), &weaponSettings.visibilityCheck, Desc("aimbot.visibility_check", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
        nav_elements::CheckboxComponent(Lbl("aimbot.humanized_smooth").c_str(), &weaponSettings.humanizedSmooth, Desc("aimbot.humanized_smooth", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
        nav_elements::CheckboxComponent(Lbl("aimbot.ignore_downed").c_str(), &weaponSettings.ignoreDowned, Desc("aimbot.ignore_downed", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON_OFF);
        nav_elements::CheckboxComponent(Lbl("aimbot.player_ai").c_str(), &weaponSettings.playerAi, Desc("aimbot.player_ai", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_SMART_TOY);
        nav_elements::CheckboxComponent(Lbl("aimbot.weapon_only").c_str(), &weaponSettings.weaponOnly, Desc("aimbot.weapon_only", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_GAVEL);
        } else {
        nav_elements::CheckboxComponent(Lbl("aimbot.enable").c_str(), &general.enabled, Desc("aimbot.enable", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TARGET);
        nav_elements::CheckboxComponent(Lbl("aimbot.aim_lock").c_str(), &general.aimLock, Desc("aimbot.aim_lock", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, true, ICON_MS_LOCK);
        nav_elements::CheckboxComponent(Lbl("aimbot.predict").c_str(), &general.predict, Desc("aimbot.predict", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TRENDING_UP);
        nav_elements::CheckboxComponent(Lbl("aimbot.save_target").c_str(), &general.saveTarget, Desc("aimbot.save_target", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_SAVE);
        nav_elements::CheckboxComponent(Lbl("aimbot.visibility_check").c_str(), &general.visibilityCheck, Desc("aimbot.visibility_check", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
        nav_elements::CheckboxComponent(Lbl("aimbot.humanized_smooth").c_str(), &general.humanizedSmooth, Desc("aimbot.humanized_smooth", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
        nav_elements::CheckboxComponent(Lbl("aimbot.ignore_downed").c_str(), &general.ignoreDowned, Desc("aimbot.ignore_downed", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON_OFF);
        nav_elements::CheckboxComponent(Lbl("aimbot.player_ai").c_str(), &general.playerAi, Desc("aimbot.player_ai", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_SMART_TOY);
        nav_elements::CheckboxComponent(Lbl("aimbot.weapon_only").c_str(), &general.weaponOnly, Desc("aimbot.weapon_only", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_GAVEL);
    }

    // Keybinds for Aimbot main toggles
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("aimbot.enable"), []() {
        std::string kbLabel = std::string(GET_TEXT("aimbot.enable")) + "##kb_aimbot_enable";
        nav_elements::Keybind(kbLabel.c_str(), "Aimbot toggle", &HOTKEY_SETTINGS.features.aimbotEnable.key, &HOTKEY_SETTINGS.features.aimbotEnable.isToggle);
    });
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("aimbot.aim_lock"), []() {
        std::string kbLabel = std::string(GET_TEXT("aimbot.aim_lock")) + "##kb_aim_lock";
        nav_elements::Keybind(kbLabel.c_str(), "Aim lock", &HOTKEY_SETTINGS.features.aimbotAimLock.key, &HOTKEY_SETTINGS.features.aimbotAimLock.isToggle);
    });

    ImGui::Dummy(ImVec2(0, 15));
    RenderSectionHeader(GET_TEXT("aimbot.title").c_str(), 0, 0);

    if (general.perWeapon) {
        int selectedWeapon = CONFIG_SETTINGS.selectedWeapon;
        AimbotCore& weaponSettings = SettingsManager::GetWeaponSetting(perWeapon, selectedWeapon, general);

        nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &weaponSettings.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
        nav_elements::ComboEx(Lbl("aimbot.hitbox").c_str(), &weaponSettings.hitBox, hitboxes, IM_ARRAYSIZE(hitboxes), Desc("aimbot.hitbox", nullptr).c_str(), ICON_MS_BODY_PART);

        nav_elements::SliderFloat(Lbl("aimbot.smooth").c_str(), &weaponSettings.smooth, 1.0f, 20.0f, Desc("aimbot.smooth", nullptr).c_str(), "%.1f", ICON_MS_SPEED);
        nav_elements::SliderFloat(Lbl("aimbot.humanized_smooth").c_str(), &weaponSettings.humanizedSmoothPercent, 1.0f, 10.0f, Desc("aimbot.humanized_smooth", nullptr).c_str(), "%.1f", ICON_MS_PERSON_SETTINGS);
    } else {
        nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &general.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);

        const char* hitboxes[] = { "Head", "Neck", "Chest", "Pelvis", "Random" };
        nav_elements::ComboEx(Lbl("aimbot.hitbox").c_str(), &general.hitBox, hitboxes, IM_ARRAYSIZE(hitboxes), Desc("aimbot.hitbox", nullptr).c_str(), ICON_MS_BODY_PART);

        nav_elements::SliderFloat(Lbl("aimbot.smooth").c_str(), &general.smooth, 1.0f, 20.0f, Desc("aimbot.smooth", nullptr).c_str(), "%.1f", ICON_MS_SPEED);
        nav_elements::SliderFloat(Lbl("aimbot.humanized_smooth").c_str(), &general.humanizedSmoothPercent, 1.0f, 10.0f, Desc("aimbot.humanized_smooth", nullptr).c_str(), "%.1f", ICON_MS_PERSON_SETTINGS);
    }

    if (general.perWeapon) {
        ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("aimbot.per_weapon").c_str(), 0, 0);

        const char* weaponTypes[] = { "DMR", "SMG", "Shotgun", "Pistol", "AssaultRifle", "Sniper" };
        nav_elements::ComboEx(Lbl("aimbot.per_weapon").c_str(), &CONFIG_SETTINGS.selectedWeapon, weaponTypes, IM_ARRAYSIZE(weaponTypes), Desc("aimbot.per_weapon", nullptr).c_str(), ICON_MS_WEAPONS);
    }

    ImGui::Dummy(ImVec2(0, 10));
    nav_elements::CheckboxComponent(Lbl("aimbot.per_weapon").c_str(), &general.perWeapon, Desc("aimbot.per_weapon", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TUNE);

    ImGui::Dummy(ImVec2(0, 15));
    RenderSectionHeader(GET_TEXT("aimbot.visuals.title").c_str(), 0, 0);

    // FOV
    nav_elements::CheckboxComponent(Lbl("aimbot.draw_fov").c_str(), &general.visuals.drawFov, Desc("aimbot.draw_fov", nullptr).c_str(), false, GetAimbotColorStates("Draw FOV"), nullptr, nullptr, false, ICON_MS_CIRCLE);
    
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("aimbot.draw_fov"), []() {
        std::vector<nav_elements::ColorState> fovColors = GetAimbotColorStates("Draw FOV");
        if (nav_elements::ColorPickerComponent("FOV Colors", fovColors, "Choose FOV colors", ICON_MS_PALETTE)) {
            if (!fovColors.empty()) {
                AIMBOT_SETTINGS.general.visuals.fovColor = ColorRGB(
                    fovColors[0].color[0], fovColors[0].color[1], fovColors[0].color[2]
                );
            }
        }
        nav_elements::CheckboxComponent(Lbl("aimbot.draw_fov_filled").c_str(), &AIMBOT_SETTINGS.general.visuals.drawFovFilled, Desc("aimbot.draw_fov_filled", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_CIRCLE_FILLED);
        nav_elements::CheckboxComponent(Lbl("aimbot.draw_fov_outline").c_str(), &AIMBOT_SETTINGS.general.visuals.drawFovOutline, Desc("aimbot.draw_fov_outline", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_CIRCLE_OUTLINE);
        nav_elements::CheckboxComponent(Lbl("aimbot.draw_fov_rgb").c_str(), &AIMBOT_SETTINGS.general.visuals.drawFovRgb, Desc("aimbot.draw_fov_rgb", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PALETTE);
        nav_elements::SliderFloat(Lbl("aimbot.fov").c_str(), &AIMBOT_SETTINGS.general.fov, 1.0f, 500.0f, Desc("aimbot.fov", nullptr).c_str(), "%.1f", ICON_MS_ZOOM_IN);
        nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_fov").c_str(), Desc("hotkeys.draw_fov", nullptr).c_str(), &HOTKEY_SETTINGS.features.aimbotDrawFov.key, &HOTKEY_SETTINGS.features.aimbotDrawFov.isToggle, ICON_MS_ZOOM_IN);
    });

    // Crosshair
    nav_elements::CheckboxComponent(Lbl("aimbot.draw_crosshair").c_str(), &general.visuals.drawCrosshair, Desc("aimbot.draw_crosshair", nullptr).c_str(), false, GetAimbotColorStates("Draw Crosshair"), nullptr, nullptr, false, ICON_MS_ADD_CIRCLE);

    nav_elements::RenderExpandedContentAnimated(GET_TEXT("aimbot.draw_crosshair"), []() {
        std::vector<nav_elements::ColorState> crosshairColors = GetAimbotColorStates("Draw Crosshair");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##crosshair").c_str(), crosshairColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (!crosshairColors.empty()) {
                AIMBOT_SETTINGS.general.visuals.crosshairColor = ColorRGB(
                    crosshairColors[0].color[0], crosshairColors[0].color[1], crosshairColors[0].color[2]
                );
            }
        }
        const char* crosshairTypes[] = { "Cross", "Circle", "Dot" };
        int crosshair = static_cast<int>(AIMBOT_SETTINGS.general.visuals.crosshairType);
        if (nav_elements::ComboEx(Lbl("aimbot.crosshair_type").c_str(), &crosshair, crosshairTypes, IM_ARRAYSIZE(crosshairTypes), Desc("aimbot.crosshair_type", nullptr).c_str(), ICON_MS_CROSSHAIRS)) {
            AIMBOT_SETTINGS.general.visuals.crosshairType = static_cast<CrosshairType>(crosshair);
        }
        nav_elements::SliderFloat(Lbl("aimbot.crosshair_size").c_str(), &AIMBOT_SETTINGS.general.visuals.drawSize, 1.0f, 50.0f, Desc("aimbot.crosshair_size", nullptr).c_str(), "%.1f", ICON_MS_ZOOM_IN);
        nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_crosshair").c_str(), Desc("hotkeys.draw_crosshair", nullptr).c_str(), &HOTKEY_SETTINGS.features.aimbotDrawCrosshair.key, &HOTKEY_SETTINGS.features.aimbotDrawCrosshair.isToggle, ICON_MS_CROSSHAIRS);
    });

    // Target indicator
    nav_elements::CheckboxComponent(Lbl("aimbot.draw_target").c_str(), &general.visuals.drawTarget, Desc("aimbot.draw_target", nullptr).c_str(), false, GetAimbotColorStates("Draw Target Line"), nullptr, nullptr, false, ICON_MS_TRENDING_FLAT);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("aimbot.draw_target"), []() {
        std::vector<nav_elements::ColorState> targetColors = GetAimbotColorStates("Draw Target Line");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##target").c_str(), targetColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (!targetColors.empty()) {
                AIMBOT_SETTINGS.general.visuals.targetColor = ColorRGB(
                    targetColors[0].color[0], targetColors[0].color[1], targetColors[0].color[2]
                );
            }
        }
        const char* targetTypes[] = { "Circle", "Square", "Triangle" };
        int targetType = static_cast<int>(AIMBOT_SETTINGS.general.visuals.targetType);
        if (nav_elements::ComboEx(Lbl("aimbot.target_type").c_str(), &targetType, targetTypes, IM_ARRAYSIZE(targetTypes), Desc("aimbot.target_type", nullptr).c_str(), ICON_MS_CROSSHAIRS)) {
            AIMBOT_SETTINGS.general.visuals.targetType = static_cast<TargetIndicatorType>(targetType);
        }
        nav_elements::SliderFloat(Lbl("visuals.thickness").c_str(), &AIMBOT_SETTINGS.general.visuals.drawThickness, 1.0f, 5.0f, Desc("visuals.thickness", nullptr).c_str(), "%.1f", ICON_MS_BRUSH);
        nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_target").c_str(), Desc("hotkeys.draw_target", nullptr).c_str(), &HOTKEY_SETTINGS.features.aimbotDrawTarget.key, &HOTKEY_SETTINGS.features.aimbotDrawTarget.isToggle, ICON_MS_TRENDING_FLAT);
    });

    // Triggerbot Section
    ImGui::Dummy(ImVec2(0, 15));
    RenderSectionHeader(GET_TEXT("triggerbot.title").c_str(), 0, 0);

    nav_elements::CheckboxComponent(Lbl("triggerbot.enable").c_str(), &TRIGGERBOT_SETTINGS.general.enabled, Desc("triggerbot.enable", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TARGET);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("triggerbot.enable"), []() {
        nav_elements::CheckboxComponent(Lbl("triggerbot.all_weapons").c_str(), &TRIGGERBOT_SETTINGS.general.enableAllWeapons, Desc("triggerbot.all_weapons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_WEAPONS);
        nav_elements::CheckboxComponent(Lbl("triggerbot.shotguns_only").c_str(), &TRIGGERBOT_SETTINGS.general.enableOnlyShotguns, Desc("triggerbot.shotguns_only", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_WEAPONS);
        nav_elements::CheckboxComponent(Lbl("aimbot.per_weapon").c_str(), &TRIGGERBOT_SETTINGS.general.perWeapon, Desc("aimbot.per_weapon", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TUNE);
        nav_elements::SliderInt(Lbl("triggerbot.delay").c_str(), &TRIGGERBOT_SETTINGS.general.delay, 0, 50, Desc("triggerbot.delay", nullptr).c_str(), "%d ms", ICON_MS_TIMER);
        nav_elements::SliderFloat(Lbl("aimbot.max_distance").c_str(), &TRIGGERBOT_SETTINGS.general.maxDistance, 1.0f, 200.0f, Desc("aimbot.max_distance", nullptr).c_str(), "%.1f m", ICON_MS_STRAIGHTEN);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.global.holdTrigger.key, &HOTKEY_SETTINGS.global.holdTrigger.isToggle);
    });
}

void CGui::RenderVisualsTab() {
    nav_elements::CheckboxComponent(Lbl("esp.players.enable").c_str(), &PLAYER_ESP_SETTINGS.enabled, Desc("esp.players.enable", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
    // General player ESP options
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.enable"), []() {    
        nav_elements::SliderInt(GET_TEXT("esp.players.max_distance").c_str(), &PLAYER_ESP_SETTINGS.maxDistance, 1, 1500, nullptr, "%d m", ICON_MS_STRAIGHTEN);
        nav_elements::CheckboxComponent(Lbl("esp.players.team_check").c_str(), &PLAYER_ESP_SETTINGS.teamCheck, Desc("esp.players.team_check", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_GROUP);
        nav_elements::CheckboxComponent(Lbl("aimbot.player_ai").c_str(), &PLAYER_ESP_SETTINGS.playerAi, Desc("aimbot.player_ai", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_SMART_TOY);
        nav_elements::CheckboxComponent(Lbl("esp.players.ignore_downed").c_str(), &PLAYER_ESP_SETTINGS.ignoreDowned, Desc("esp.players.ignore_downed", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON_OFF);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspEnable.key, &HOTKEY_SETTINGS.features.playerEspEnable.isToggle);
    });
    
    // Bounding Box with integrated settings
    nav_elements::CheckboxComponent(Lbl("esp.players.box").c_str(), &PLAYER_ESP_SETTINGS.box.enabled, Desc("esp.players.box", nullptr).c_str(), false, GetPlayerColorStates("Bounding Box"), nullptr, nullptr, false, ICON_MS_SQUARE);

    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.box"), []() {
        float boxThickness = PLAYER_ESP_SETTINGS.box.thickness;
        int boxType = static_cast<int>(PLAYER_ESP_SETTINGS.box.type == BoxType::Corner ? 0 : 1);

        std::vector<nav_elements::ColorState> boxColors = GetPlayerColorStates("Bounding Box");
        if (nav_elements::ColorPickerComponent("Box Colors", boxColors, "Choose box colors", ICON_MS_PALETTE)) {
            if (boxColors.size() >= 4) {
                PLAYER_ESP_SETTINGS.box.visibleColor = ColorRGBA(boxColors[0].color[0], boxColors[0].color[1], boxColors[0].color[2], boxColors[0].color[3]);
                PLAYER_ESP_SETTINGS.box.nonVisibleColor = ColorRGBA(boxColors[1].color[0], boxColors[1].color[1], boxColors[1].color[2], boxColors[1].color[3]);
                PLAYER_ESP_SETTINGS.box.knockedVisibleColor = ColorRGBA(boxColors[2].color[0], boxColors[2].color[1], boxColors[2].color[2], boxColors[2].color[3]);
                PLAYER_ESP_SETTINGS.box.knockedNonVisibleColor = ColorRGBA(boxColors[3].color[0], boxColors[3].color[1], boxColors[3].color[2], boxColors[3].color[3]);
            }
        }
                
        const char* boxTypes[] = { "Bounding Box", "Corner Box" };
        if (nav_elements::ComboEx(Lbl("visuals.box_type").c_str(), &boxType, boxTypes, IM_ARRAYSIZE(boxTypes), Desc("visuals.box_type", nullptr).c_str(), ICON_MS_CROP_SQUARE)) {
            PLAYER_ESP_SETTINGS.box.type = (boxType == 1) ? BoxType::Rounded : BoxType::Corner;
        }
        if (nav_elements::SliderFloat(GET_TEXT("visuals.thickness").c_str(), &boxThickness, 1.0f, 5.0f, nullptr, "%.1f", ICON_MS_LINE_WEIGHT)) {
            PLAYER_ESP_SETTINGS.box.thickness = boxThickness;
        }
        nav_elements::CheckboxComponent(Lbl("visuals.filled").c_str(), &PLAYER_ESP_SETTINGS.box.filled, Desc("visuals.filled", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_CROP_SQUARE);
        nav_elements::SliderFloat(Lbl("visuals.rounding").c_str(), &PLAYER_ESP_SETTINGS.box.rounding, 0.0f, 15.0f, Desc("visuals.rounding", nullptr).c_str(), "%.1f", ICON_MS_CROP_SQUARE);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspBox.key, &HOTKEY_SETTINGS.features.playerEspBox.isToggle);
    });

    // Skeleton with integrated settings
    nav_elements::CheckboxComponent(Lbl("esp.players.skeleton").c_str(), &PLAYER_ESP_SETTINGS.skeleton.enabled, Desc("esp.players.skeleton", nullptr).c_str(), false, GetPlayerColorStates("Skeleton"), nullptr, nullptr, false, ICON_MS_SKELETON);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.skeleton"), []() {
        float skeletonThickness = PLAYER_ESP_SETTINGS.skeleton.thickness;
        if (nav_elements::SliderFloat(GET_TEXT("visuals.thickness").c_str(), &skeletonThickness, 1.0f, 5.0f, nullptr, "%.1f", ICON_MS_LINE_WEIGHT)) {
            PLAYER_ESP_SETTINGS.skeleton.thickness = skeletonThickness;
        }
        // Colors for skeleton
        std::vector<nav_elements::ColorState> skColors = GetPlayerColorStates("Skeleton");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##skeleton").c_str(), skColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (skColors.size() >= 4) {
                PLAYER_ESP_SETTINGS.skeleton.visibleColor = ColorRGB(skColors[0].color[0], skColors[0].color[1], skColors[0].color[2]);
                PLAYER_ESP_SETTINGS.skeleton.nonVisibleColor = ColorRGB(skColors[1].color[0], skColors[1].color[1], skColors[1].color[2]);
                PLAYER_ESP_SETTINGS.skeleton.knockedVisibleColor = ColorRGB(skColors[2].color[0], skColors[2].color[1], skColors[2].color[2]);
                PLAYER_ESP_SETTINGS.skeleton.knockedNonVisibleColor = ColorRGB(skColors[3].color[0], skColors[3].color[1], skColors[3].color[2]);
            }
        }
        // Curved and rounding
        nav_elements::CheckboxComponent(Lbl("visuals.curved").c_str(), &PLAYER_ESP_SETTINGS.skeleton.curved, Desc("visuals.curved", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_TRENDING_UP);
        nav_elements::SliderFloat(Lbl("visuals.rounding").c_str(), &PLAYER_ESP_SETTINGS.skeleton.rounding, 0.0f, 20.0f, Desc("visuals.rounding", nullptr).c_str(), "%.1f", ICON_MS_CROP_SQUARE);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspSkeleton.key, &HOTKEY_SETTINGS.features.playerEspSkeleton.isToggle);
    });

    // Names
    nav_elements::CheckboxComponent(Lbl("esp.players.nickname").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.enabled, Desc("esp.players.nickname", nullptr).c_str(), false, GetPlayerColorStates("Names"), nullptr, nullptr, false, ICON_MS_PERSON);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.nickname"), []() {
        // Text color settings via states map
        std::vector<nav_elements::ColorState> nameColors = GetPlayerColorStates("Names");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##name").c_str(), nameColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (!nameColors.empty()) {
                PLAYER_ESP_SETTINGS.info.nickname.font.color = ColorRGBA(
                    nameColors[0].color[0], nameColors[0].color[1], nameColors[0].color[2], nameColors[0].color[3]
                );
            }
        }
        // Font tuning
        nav_elements::SliderFloat(GET_TEXT("visuals.font_size").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.font.size, 8.0f, 32.0f, nullptr, "%.1f", ICON_MS_FORMAT_SIZE);
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.font.bold, Desc("visuals.bold", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_FORMAT_BOLD);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.font.italic, Desc("visuals.italic", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_FORMAT_ITALIC);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.font.outline, Desc("visuals.outline", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_BORDER_COLOR);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &PLAYER_ESP_SETTINGS.info.nickname.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", nullptr).c_str(), "%.1f", ICON_MS_LINE_WEIGHT);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspNickname.key, &HOTKEY_SETTINGS.features.playerEspNickname.isToggle);
    });

    // Distance
    nav_elements::CheckboxComponent(Lbl("esp.players.distance").c_str(), &PLAYER_ESP_SETTINGS.info.distance.enabled, Desc("esp.players.distance", nullptr).c_str(), false, GetPlayerColorStates("Distance"), nullptr, nullptr, false, ICON_MS_STRAIGHTEN);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.distance"), []() {
        std::vector<nav_elements::ColorState> distColors = GetPlayerColorStates("Distance");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##distance").c_str(), distColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (!distColors.empty()) {
                PLAYER_ESP_SETTINGS.info.distance.font.color = ColorRGBA(
                    distColors[0].color[0], distColors[0].color[1], distColors[0].color[2], distColors[0].color[3]
                );
            }
        }
        nav_elements::SliderFloat(GET_TEXT("visuals.font_size").c_str(), &PLAYER_ESP_SETTINGS.info.distance.font.size, 8.0f, 32.0f, nullptr, "%.1f", ICON_MS_FORMAT_SIZE);
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &PLAYER_ESP_SETTINGS.info.distance.font.bold, Desc("visuals.bold", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_FORMAT_BOLD);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &PLAYER_ESP_SETTINGS.info.distance.font.italic, Desc("visuals.italic", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_FORMAT_ITALIC);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &PLAYER_ESP_SETTINGS.info.distance.font.outline, Desc("visuals.outline", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_BORDER_COLOR);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &PLAYER_ESP_SETTINGS.info.distance.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", nullptr).c_str(), "%.1f", ICON_MS_LINE_WEIGHT);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspDistance.key, &HOTKEY_SETTINGS.features.playerEspDistance.isToggle);
    });

    // Head Circle
    nav_elements::CheckboxComponent(Lbl("esp.players.head_circle").c_str(), &PLAYER_ESP_SETTINGS.headCircle.enabled, Desc("esp.players.head_circle", nullptr).c_str(), false, GetPlayerColorStates("Head Circle"), nullptr, nullptr, false, ICON_MS_CIRCLE);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.head_circle"), []() {
        std::vector<nav_elements::ColorState> headColors = GetPlayerColorStates("Head Circle");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##head").c_str(), headColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (headColors.size() >= 4) {
                PLAYER_ESP_SETTINGS.headCircle.visibleColor = ColorRGB(headColors[0].color[0], headColors[0].color[1], headColors[0].color[2]);
                PLAYER_ESP_SETTINGS.headCircle.nonVisibleColor = ColorRGB(headColors[1].color[0], headColors[1].color[1], headColors[1].color[2]);
                PLAYER_ESP_SETTINGS.headCircle.knockedVisibleColor = ColorRGB(headColors[2].color[0], headColors[2].color[1], headColors[2].color[2]);
                PLAYER_ESP_SETTINGS.headCircle.knockedNonVisibleColor = ColorRGB(headColors[3].color[0], headColors[3].color[1], headColors[3].color[2]);
            }
        }
        nav_elements::SliderFloat(Lbl("visuals.thickness").c_str(), &PLAYER_ESP_SETTINGS.headCircle.thickness, 1.0f, 5.0f, Desc("visuals.thickness", nullptr).c_str(), "%.1f", ICON_MS_LINE_WEIGHT);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspHeadCircle.key, &HOTKEY_SETTINGS.features.playerEspHeadCircle.isToggle);
    });

    // Snapline
    nav_elements::CheckboxComponent(Lbl("esp.players.lines").c_str(), &PLAYER_ESP_SETTINGS.lines.enabled, Desc("esp.players.lines", nullptr).c_str(), false, GetPlayerColorStates("Snapline"), nullptr, nullptr, false, ICON_MS_TRENDING_FLAT);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.players.lines"), []() {
        std::vector<nav_elements::ColorState> lineColors = GetPlayerColorStates("Snapline");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##line").c_str(), lineColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (lineColors.size() >= 4) {
                PLAYER_ESP_SETTINGS.lines.visibleColor = ColorRGB(lineColors[0].color[0], lineColors[0].color[1], lineColors[0].color[2]);
                PLAYER_ESP_SETTINGS.lines.nonVisibleColor = ColorRGB(lineColors[1].color[0], lineColors[1].color[1], lineColors[1].color[2]);
                PLAYER_ESP_SETTINGS.lines.knockedVisibleColor = ColorRGB(lineColors[2].color[0], lineColors[2].color[1], lineColors[2].color[2]);
                PLAYER_ESP_SETTINGS.lines.knockedNonVisibleColor = ColorRGB(lineColors[3].color[0], lineColors[3].color[1], lineColors[3].color[2]);
            }
        }
        int pos = static_cast<int>(PLAYER_ESP_SETTINGS.lines.position);
        const char* posItems[] = { "Top", "Center", "Bottom" };
        if (nav_elements::ComboEx(Lbl("visuals.line_position").c_str(), &pos, posItems, IM_ARRAYSIZE(posItems), Desc("visuals.line_position", nullptr).c_str(), ICON_MS_TRENDING_FLAT)) {
            PLAYER_ESP_SETTINGS.lines.position = static_cast<LinePosition>(pos);
        }
        nav_elements::SliderFloat(Lbl("visuals.thickness").c_str(), &PLAYER_ESP_SETTINGS.lines.thickness, 1.0f, 5.0f, Desc("visuals.thickness", nullptr).c_str(), "%.1f", ICON_MS_LINE_WEIGHT);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.playerEspLines.key, &HOTKEY_SETTINGS.features.playerEspLines.isToggle);
    });
}

void CGui::RenderMainTab() {
    // Tab selectors for loot categories
    static int lootTab = 0;
    ImGui::BeginGroup();

    if (nav_elements::HorizontalTab(GET_TEXT("esp.items.consumables").c_str(), &lootTab, 0)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab(GET_TEXT("esp.items.weapons").c_str(), &lootTab, 1)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab(GET_TEXT("esp.items.ammo").c_str(), &lootTab, 2)) {}
    ImGui::SameLine(0, 10);
    if (nav_elements::HorizontalTab(GET_TEXT("esp.items.others").c_str(), &lootTab, 3)) {}

    ImGui::EndGroup();

        ImGui::Dummy(ImVec2(0, 10));

    // Show selected tab content
    switch (lootTab) {
        case 0: // Consumables
            RenderConsumablesTab();
            break;
        case 1: // Weapons
            RenderWeaponsTab();
            break;
        case 2: // Ammo
            RenderAmmoTab();
            break;
        case 3: // Other Items
            RenderOtherItemsTab();
            break;
    }
}

void CGui::RenderConsumablesTab() {
        // Consumables section
        //RenderSectionHeader("Consumables", 0, 0);

        // Consumable settings
    nav_elements::CheckboxComponent(Lbl("esp.items.consumables").c_str(), &ITEM_ESP_SETTINGS.consumables.enabled, Desc("esp.items.consumables", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_name").c_str(), &ITEM_ESP_SETTINGS.consumables.display.enabled, Desc("esp.items.show_name", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_distance").c_str(), &ITEM_ESP_SETTINGS.consumables.display.showDistance, Desc("esp.items.show_distance", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_STRAIGHTEN);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_icons").c_str(), &ITEM_ESP_SETTINGS.consumables.display.showIcons, Desc("esp.items.show_icons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_IMAGE);

    nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &ITEM_ESP_SETTINGS.consumables.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.items.consumables"), []() {
        nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &ITEM_ESP_SETTINGS.consumables.display.font.size, 10.0f, 25.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &ITEM_ESP_SETTINGS.consumables.display.font.bold, Desc("visuals.bold", ICON_MS_FORMAT_BOLD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &ITEM_ESP_SETTINGS.consumables.display.font.italic, Desc("visuals.italic", ICON_MS_FORMAT_ITALIC).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &ITEM_ESP_SETTINGS.consumables.display.font.outline, Desc("visuals.outline", ICON_MS_BORDER_COLOR).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &ITEM_ESP_SETTINGS.consumables.display.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", ICON_MS_LINE_WEIGHT).c_str(), "%.1f");
        nav_elements::SliderFloat(Lbl("visuals.icon_size").c_str(), &ITEM_ESP_SETTINGS.consumables.display.iconSize, 5.0f, 40.0f, Desc("visuals.icon_size", ICON_MS_ZOOM_IN).c_str(), "%.1f");
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.itemConsumableEnable.key, &HOTKEY_SETTINGS.features.itemConsumableEnable.isToggle);
    });

    // Consumable items selection
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("esp.items.consumables").c_str(), 0, 0);

    bool bandages = ITEM_ESP_SETTINGS.consumables.items["Bandages"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.bandages").c_str(), &bandages, Desc("items.consumables.bandages", ICON_MS_HEALING).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["Bandages"] = bandages; }
    bool medkit = ITEM_ESP_SETTINGS.consumables.items["Medkit"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.medkit").c_str(), &medkit, Desc("items.consumables.medkit", ICON_MS_MEDICAL_SERVICES).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["Medkit"] = medkit; }
    bool ssp = ITEM_ESP_SETTINGS.consumables.items["SmallShieldPotion"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.small_shield").c_str(), &ssp, Desc("items.consumables.small_shield", ICON_MS_SHIELD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["SmallShieldPotion"] = ssp; }
    bool sp = ITEM_ESP_SETTINGS.consumables.items["ShieldPotion"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.big_shield").c_str(), &sp, Desc("items.consumables.big_shield", ICON_MS_SHIELD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["ShieldPotion"] = sp; }
    bool chug = ITEM_ESP_SETTINGS.consumables.items["ChugSplash"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.chug_splash").c_str(), &chug, Desc("items.consumables.chug_splash", ICON_MS_WATER_DROP).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["ChugSplash"] = chug; }
    bool nitro = ITEM_ESP_SETTINGS.consumables.items["NitroSplash"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.nitro_splash").c_str(), &nitro, Desc("items.consumables.nitro_splash", ICON_MS_SCIENCE).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["NitroSplash"] = nitro; }
    bool flow = ITEM_ESP_SETTINGS.consumables.items["FlowBerryFizz"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.flowberry_fizz").c_str(), &flow, Desc("items.consumables.flowberry_fizz", ICON_MS_BUBBLE_CHART).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["FlowBerryFizz"] = flow; }
    bool nuka = ITEM_ESP_SETTINGS.consumables.items["NukaCola"]; if (nav_elements::CheckboxComponent(Lbl("items.consumables.nuka_cola").c_str(), &nuka, Desc("items.consumables.nuka_cola", ICON_MS_LOCAL_DRINK).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.consumables.items["NukaCola"] = nuka; }
}

void CGui::RenderWeaponsTab() {
        // Weapon settings
    nav_elements::CheckboxComponent(Lbl("esp.items.weapons").c_str(), &ITEM_ESP_SETTINGS.weapons.enabled, Desc("esp.items.weapons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_name").c_str(), &ITEM_ESP_SETTINGS.weapons.display.enabled, Desc("esp.items.show_name", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_distance").c_str(), &ITEM_ESP_SETTINGS.weapons.display.showDistance, Desc("esp.items.show_distance", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_STRAIGHTEN);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_icons").c_str(), &ITEM_ESP_SETTINGS.weapons.display.showIcons, Desc("esp.items.show_icons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_IMAGE);

    nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &ITEM_ESP_SETTINGS.weapons.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.items.weapons"), []() {
        nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &ITEM_ESP_SETTINGS.weapons.display.font.size, 10.0f, 25.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &ITEM_ESP_SETTINGS.weapons.display.font.bold, Desc("visuals.bold", ICON_MS_FORMAT_BOLD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &ITEM_ESP_SETTINGS.weapons.display.font.italic, Desc("visuals.italic", ICON_MS_FORMAT_ITALIC).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &ITEM_ESP_SETTINGS.weapons.display.font.outline, Desc("visuals.outline", ICON_MS_BORDER_COLOR).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &ITEM_ESP_SETTINGS.weapons.display.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", ICON_MS_LINE_WEIGHT).c_str(), "%.1f");
        nav_elements::SliderFloat(Lbl("visuals.icon_size").c_str(), &ITEM_ESP_SETTINGS.weapons.display.iconSize, 5.0f, 40.0f, Desc("visuals.icon_size", ICON_MS_ZOOM_IN).c_str(), "%.1f");
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.itemWeaponEnable.key, &HOTKEY_SETTINGS.features.itemWeaponEnable.isToggle);
    });

        // Game mode selection
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("config.game_mode").c_str(), 0, 0);

        const char* gameModes[] = { "Remix Battle Royale", "Other Modes" };
    int modeIdx = static_cast<int>(CONFIG_SETTINGS.gameMode);
    if (nav_elements::ComboEx(Lbl("game.mode").c_str(), &modeIdx, gameModes, IM_ARRAYSIZE(gameModes), Desc("game.mode", nullptr).c_str(), ICON_MS_SPORTS_ESPORTS)) {
        CONFIG_SETTINGS.gameMode = static_cast<GameMode>(modeIdx);
    }

        // Weapon display selection
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("esp.items.weapons").c_str(), 0, 0);

    if (static_cast<int>(CONFIG_SETTINGS.gameMode) == 0) { // Remix Battle Royale
        bool newPump = ITEM_ESP_SETTINGS.weapons.items["NewPump_Shotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.new_pump_shotgun").c_str(), &newPump, Desc("items.weapons.new_pump_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["NewPump_Shotgun"] = newPump; }
        bool ogPump = ITEM_ESP_SETTINGS.weapons.items["OGPump_Shotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.og_pump_shotgun").c_str(), &ogPump, Desc("items.weapons.og_pump_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["OGPump_Shotgun"] = ogPump; }
        bool submg = ITEM_ESP_SETTINGS.weapons.items["SubmachineGun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.submachine_gun").c_str(), &submg, Desc("items.weapons.submachine_gun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["SubmachineGun"] = submg; }
        bool dualPistols = ITEM_ESP_SETTINGS.weapons.items["DualPistols"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.dual_pistols").c_str(), &dualPistols, Desc("items.weapons.dual_pistols", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["DualPistols"] = dualPistols; }
        bool rapidFireSMG = ITEM_ESP_SETTINGS.weapons.items["RapidFireSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.rapid_fire_smg").c_str(), &rapidFireSMG, Desc("items.weapons.rapid_fire_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["RapidFireSMG"] = rapidFireSMG; }
        bool suppressedSMG = ITEM_ESP_SETTINGS.weapons.items["SuppressedSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.suppressed_smg").c_str(), &suppressedSMG, Desc("items.weapons.suppressed_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["SuppressedSMG"] = suppressedSMG; }
        bool supAR = ITEM_ESP_SETTINGS.weapons.items["SuppressedAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.suppressed_assault_rifle").c_str(), &supAR, Desc("items.weapons.suppressed_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["SuppressedAssaultRifle"] = supAR; }
        bool heavySniper = ITEM_ESP_SETTINGS.weapons.items["HeavySniperRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.heavy_sniper_rifle").c_str(), &heavySniper, Desc("items.weapons.heavy_sniper_rifle", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HeavySniperRifle"] = heavySniper; }
        bool semiAuto = ITEM_ESP_SETTINGS.weapons.items["SemiAutomaticSniperRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.semi_auto_sniper_rifle").c_str(), &semiAuto, Desc("items.weapons.semi_auto_sniper_rifle", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["SemiAutomaticSniperRifle"] = semiAuto; }
        bool grenadeLauncher = ITEM_ESP_SETTINGS.weapons.items["GrenadeLauncher"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.grenade_launcher").c_str(), &grenadeLauncher, Desc("items.weapons.grenade_launcher", ICON_MS_ROCKET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["GrenadeLauncher"] = grenadeLauncher; }
        bool remoteExplosives = ITEM_ESP_SETTINGS.weapons.items["RemoteExplosives"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.remote_explosives").c_str(), &remoteExplosives, Desc("items.weapons.remote_explosives", ICON_MS_DANGEROUS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["RemoteExplosives"] = remoteExplosives; }
        bool regularGrenades = ITEM_ESP_SETTINGS.weapons.items["RegularGrenades"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.regular_grenades").c_str(), &regularGrenades, Desc("items.weapons.regular_grenades", ICON_MS_DANGEROUS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["RegularGrenades"] = regularGrenades; }
        bool rangerPistol = ITEM_ESP_SETTINGS.weapons.items["RangerPistol"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.ranger_pistol").c_str(), &rangerPistol, Desc("items.weapons.ranger_pistol", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["RangerPistol"] = rangerPistol; }
        bool harbingerSMG = ITEM_ESP_SETTINGS.weapons.items["HarbingerSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.harbinger_smg").c_str(), &harbingerSMG, Desc("items.weapons.harbinger_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HarbingerSMG"] = harbingerSMG; }
        bool thunderBurstSMG = ITEM_ESP_SETTINGS.weapons.items["ThunderBurstSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.thunder_burst_smg").c_str(), &thunderBurstSMG, Desc("items.weapons.thunder_burst_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["ThunderBurstSMG"] = thunderBurstSMG; }
        bool warforgedAR = ITEM_ESP_SETTINGS.weapons.items["WarforgedAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.warforged_assault_rifle").c_str(), &warforgedAR, Desc("items.weapons.warforged_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["WarforgedAssaultRifle"] = warforgedAR; }
        bool tacticalAR = ITEM_ESP_SETTINGS.weapons.items["TacticalAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.tactical_assault_rifle").c_str(), &tacticalAR, Desc("items.weapons.tactical_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["TacticalAssaultRifle"] = tacticalAR; }
        bool combatAR = ITEM_ESP_SETTINGS.weapons.items["CombatAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.combat_assault_rifle").c_str(), &combatAR, Desc("items.weapons.combat_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["CombatAssaultRifle"] = combatAR; }
        bool combatShotgun = ITEM_ESP_SETTINGS.weapons.items["CombatShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.combat_shotgun").c_str(), &combatShotgun, Desc("items.weapons.combat_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["CombatShotgun"] = combatShotgun; }
        bool gatekeeper = ITEM_ESP_SETTINGS.weapons.items["GatekeeperShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.gatekeeper_shotgun").c_str(), &gatekeeper, Desc("items.weapons.gatekeeper_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["GatekeeperShotgun"] = gatekeeper; }
        bool hammerPump = ITEM_ESP_SETTINGS.weapons.items["HammerPumpShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.hammer_pump_shotgun").c_str(), &hammerPump, Desc("items.weapons.hammer_pump_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HammerPumpShotgun"] = hammerPump; }
        bool huntressDMR = ITEM_ESP_SETTINGS.weapons.items["HuntressDMR"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.huntress_dmr").c_str(), &huntressDMR, Desc("items.weapons.huntress_dmr", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HuntressDMR"] = huntressDMR; }
        bool heavyImpact = ITEM_ESP_SETTINGS.weapons.items["HeavyImpactSniperRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.heavy_impact_sniper_rifle").c_str(), &heavyImpact, Desc("items.weapons.heavy_impact_sniper_rifle", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HeavyImpactSniperRifle"] = heavyImpact; }
        } else { // Other Modes
        bool rangerPistol = ITEM_ESP_SETTINGS.weapons.items["RangerPistol"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.ranger_pistol").c_str(), &rangerPistol, Desc("items.weapons.ranger_pistol", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["RangerPistol"] = rangerPistol; }
        bool harbingerSMG = ITEM_ESP_SETTINGS.weapons.items["HarbingerSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.harbinger_smg").c_str(), &harbingerSMG, Desc("items.weapons.harbinger_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HarbingerSMG"] = harbingerSMG; }
        bool thunderBurstSMG = ITEM_ESP_SETTINGS.weapons.items["ThunderBurstSMG"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.thunder_burst_smg").c_str(), &thunderBurstSMG, Desc("items.weapons.thunder_burst_smg", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["ThunderBurstSMG"] = thunderBurstSMG; }
        bool warforgedAR = ITEM_ESP_SETTINGS.weapons.items["WarforgedAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.warforged_assault_rifle").c_str(), &warforgedAR, Desc("items.weapons.warforged_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["WarforgedAssaultRifle"] = warforgedAR; }
        bool tacticalAR = ITEM_ESP_SETTINGS.weapons.items["TacticalAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.tactical_assault_rifle").c_str(), &tacticalAR, Desc("items.weapons.tactical_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["TacticalAssaultRifle"] = tacticalAR; }
        bool combatAR = ITEM_ESP_SETTINGS.weapons.items["CombatAssaultRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.combat_assault_rifle").c_str(), &combatAR, Desc("items.weapons.combat_assault_rifle", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["CombatAssaultRifle"] = combatAR; }
        bool combatShotgun = ITEM_ESP_SETTINGS.weapons.items["CombatShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.combat_shotgun").c_str(), &combatShotgun, Desc("items.weapons.combat_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["CombatShotgun"] = combatShotgun; }
        bool gatekeeper = ITEM_ESP_SETTINGS.weapons.items["GatekeeperShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.gatekeeper_shotgun").c_str(), &gatekeeper, Desc("items.weapons.gatekeeper_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["GatekeeperShotgun"] = gatekeeper; }
        bool hammerPump = ITEM_ESP_SETTINGS.weapons.items["HammerPumpShotgun"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.hammer_pump_shotgun").c_str(), &hammerPump, Desc("items.weapons.hammer_pump_shotgun", ICON_MS_WEAPONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HammerPumpShotgun"] = hammerPump; }
        bool huntressDMR = ITEM_ESP_SETTINGS.weapons.items["HuntressDMR"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.huntress_dmr").c_str(), &huntressDMR, Desc("items.weapons.huntress_dmr", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HuntressDMR"] = huntressDMR; }
        bool heavyImpact = ITEM_ESP_SETTINGS.weapons.items["HeavyImpactSniperRifle"]; if (nav_elements::CheckboxComponent(Lbl("items.weapons.heavy_impact_sniper_rifle").c_str(), &heavyImpact, Desc("items.weapons.heavy_impact_sniper_rifle", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.weapons.items["HeavyImpactSniperRifle"] = heavyImpact; }
        }

        // Rarity filters
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("esp.items.rarity_filter").c_str(), 0, 0);

        const char* rarities[] = { "Common", "Uncommon", "Rare", "Epic", "Legendary", "Mythic" };
        for (int i = 0; i < 6; i++) {
        if (i > 0) ImGui::SameLine();
        nav_elements::CheckboxComponent(rarities[i], &ITEM_ESP_SETTINGS.rarityFilter[i], (std::string(rarities[i]) + "^" + std::string(ICON_MS_DIAMOND)).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
    }
}

void CGui::RenderAmmoTab() {
        // Ammo section
        //RenderSectionHeader("Ammo", 0, 0);

        // Ammo settings
    nav_elements::CheckboxComponent(Lbl("esp.items.ammo").c_str(), &ITEM_ESP_SETTINGS.ammo.enabled, Desc("esp.items.ammo", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_name").c_str(), &ITEM_ESP_SETTINGS.ammo.display.enabled, Desc("esp.items.show_name", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_distance").c_str(), &ITEM_ESP_SETTINGS.ammo.display.showDistance, Desc("esp.items.show_distance", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_STRAIGHTEN);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_icons").c_str(), &ITEM_ESP_SETTINGS.ammo.display.showIcons, Desc("esp.items.show_icons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_IMAGE);

    nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &ITEM_ESP_SETTINGS.ammo.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.items.ammo"), []() {
        nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &ITEM_ESP_SETTINGS.ammo.display.font.size, 10.0f, 25.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &ITEM_ESP_SETTINGS.ammo.display.font.bold, Desc("visuals.bold", ICON_MS_FORMAT_BOLD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &ITEM_ESP_SETTINGS.ammo.display.font.italic, Desc("visuals.italic", ICON_MS_FORMAT_ITALIC).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &ITEM_ESP_SETTINGS.ammo.display.font.outline, Desc("visuals.outline", ICON_MS_BORDER_COLOR).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &ITEM_ESP_SETTINGS.ammo.display.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", ICON_MS_LINE_WEIGHT).c_str(), "%.1f");
        nav_elements::SliderFloat(Lbl("visuals.icon_size").c_str(), &ITEM_ESP_SETTINGS.ammo.display.iconSize, 5.0f, 40.0f, Desc("visuals.icon_size", ICON_MS_ZOOM_IN).c_str(), "%.1f");
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.itemAmmoEnable.key, &HOTKEY_SETTINGS.features.itemAmmoEnable.isToggle);
    });

        // Ammo types
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("esp.items.ammo").c_str(), 0, 0);

    bool ammoLight = ITEM_ESP_SETTINGS.ammo.items["AmmoLight"]; if (nav_elements::CheckboxComponent(Lbl("items.ammo.light").c_str(), &ammoLight, Desc("items.ammo.light", ICON_MS_BULLET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.ammo.items["AmmoLight"] = ammoLight; }
    bool ammoMedium = ITEM_ESP_SETTINGS.ammo.items["AmmoMedium"]; if (nav_elements::CheckboxComponent(Lbl("items.ammo.medium").c_str(), &ammoMedium, Desc("items.ammo.medium", ICON_MS_BULLET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.ammo.items["AmmoMedium"] = ammoMedium; }
    bool ammoHeavy = ITEM_ESP_SETTINGS.ammo.items["AmmoHeavy"]; if (nav_elements::CheckboxComponent(Lbl("items.ammo.heavy").c_str(), &ammoHeavy, Desc("items.ammo.heavy", ICON_MS_BULLET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.ammo.items["AmmoHeavy"] = ammoHeavy; }
    bool ammoShells = ITEM_ESP_SETTINGS.ammo.items["AmmoShells"]; if (nav_elements::CheckboxComponent(Lbl("items.ammo.shells").c_str(), &ammoShells, Desc("items.ammo.shells", ICON_MS_BULLET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.ammo.items["AmmoShells"] = ammoShells; }
    bool ammoRockets = ITEM_ESP_SETTINGS.ammo.items["AmmoRockets"]; if (nav_elements::CheckboxComponent(Lbl("items.ammo.rockets").c_str(), &ammoRockets, Desc("items.ammo.rockets", ICON_MS_ROCKET).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.ammo.items["AmmoRockets"] = ammoRockets; }
}

void CGui::RenderOtherItemsTab() {
        // Other items section
        //RenderSectionHeader("Other Items", 0, 0);

        // Other items settings
    nav_elements::CheckboxComponent(Lbl("esp.items.others").c_str(), &ITEM_ESP_SETTINGS.others.enabled, Desc("esp.items.others", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_VISIBILITY);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_name").c_str(), &ITEM_ESP_SETTINGS.others.display.enabled, Desc("esp.items.show_name", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_PERSON);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_distance").c_str(), &ITEM_ESP_SETTINGS.others.display.showDistance, Desc("esp.items.show_distance", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_STRAIGHTEN);
    nav_elements::CheckboxComponent(Lbl("esp.items.show_icons").c_str(), &ITEM_ESP_SETTINGS.others.display.showIcons, Desc("esp.items.show_icons", nullptr).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false, ICON_MS_IMAGE);

    nav_elements::SliderInt(Lbl("aimbot.max_distance").c_str(), &ITEM_ESP_SETTINGS.others.maxDistance, 1, 1000, Desc("aimbot.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("esp.items.others"), []() {
        nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &ITEM_ESP_SETTINGS.others.display.font.size, 10.0f, 25.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");
        nav_elements::CheckboxComponent(Lbl("visuals.bold").c_str(), &ITEM_ESP_SETTINGS.others.display.font.bold, Desc("visuals.bold", ICON_MS_FORMAT_BOLD).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.italic").c_str(), &ITEM_ESP_SETTINGS.others.display.font.italic, Desc("visuals.italic", ICON_MS_FORMAT_ITALIC).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("visuals.outline").c_str(), &ITEM_ESP_SETTINGS.others.display.font.outline, Desc("visuals.outline", ICON_MS_BORDER_COLOR).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::SliderFloat(Lbl("visuals.outline_thickness").c_str(), &ITEM_ESP_SETTINGS.others.display.font.outlineThickness, 0.0f, 3.0f, Desc("visuals.outline_thickness", ICON_MS_LINE_WEIGHT).c_str(), "%.1f");
        nav_elements::SliderFloat(Lbl("visuals.icon_size").c_str(), &ITEM_ESP_SETTINGS.others.display.iconSize, 5.0f, 40.0f, Desc("visuals.icon_size", ICON_MS_ZOOM_IN).c_str(), "%.1f");
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.itemOtherEnable.key, &HOTKEY_SETTINGS.features.itemOtherEnable.isToggle);
    });

        // Other item types
    ImGui::Dummy(ImVec2(0, 15));
        RenderSectionHeader(GET_TEXT("esp.items.others").c_str(), 0, 0);

    bool chest = ITEM_ESP_SETTINGS.others.items["Chest"]; if (nav_elements::CheckboxComponent(Lbl("items.others.chests").c_str(), &chest, Desc("items.others.chests", ICON_MS_INVENTORY).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.others.items["Chest"] = chest; }
    bool vehicle = ITEM_ESP_SETTINGS.others.items["Vehicle"]; if (nav_elements::CheckboxComponent(Lbl("items.others.vehicles").c_str(), &vehicle, Desc("items.others.vehicles", ICON_MS_DIRECTIONS_CAR).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.others.items["Vehicle"] = vehicle; }
    bool llama = ITEM_ESP_SETTINGS.others.items["Llama"]; if (nav_elements::CheckboxComponent(Lbl("items.others.llamas").c_str(), &llama, Desc("items.others.llamas", ICON_MS_PETS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.others.items["Llama"] = llama; }
    bool supplyDrop = ITEM_ESP_SETTINGS.others.items["SupplyDrop"]; if (nav_elements::CheckboxComponent(Lbl("items.others.supply_drops").c_str(), &supplyDrop, Desc("items.others.supply_drops", ICON_MS_INVENTORY_2).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false)) { ITEM_ESP_SETTINGS.others.items["SupplyDrop"] = supplyDrop; }
}

void CGui::RenderRadarTab() {
    // Radar section
    //RenderSectionHeader("Radar", 0, 0);

    // Radar with integrated settings
    nav_elements::CheckboxComponent(Lbl("radar.enable").c_str(), &RADAR_SETTINGS.enabled, Desc("radar.enable", ICON_MS_RADAR).c_str(), false, GetRadarColorStates("Enable Radar"), nullptr, nullptr, false);
    
    // Expanded content for Radar with integrated color picker
    nav_elements::RenderExpandedContentAnimated(GET_TEXT("radar.enable"), []() {
        // Color picker integrated into settings
        std::vector<nav_elements::ColorState> radarColors = GetRadarColorStates("Enable Radar");
        if (nav_elements::ColorPickerComponent((std::string(GET_TEXT("visuals.colors")) + "##radar").c_str(), radarColors, Desc("visuals.colors", nullptr).c_str(), ICON_MS_PALETTE)) {
            if (radarColors.size() >= 4) {
                RADAR_SETTINGS.visibleColor = ColorRGB(radarColors[0].color[0], radarColors[0].color[1], radarColors[0].color[2]);
                RADAR_SETTINGS.nonVisibleColor = ColorRGB(radarColors[1].color[0], radarColors[1].color[1], radarColors[1].color[2]);
                RADAR_SETTINGS.knockedVisibleColor = ColorRGB(radarColors[2].color[0], radarColors[2].color[1], radarColors[2].color[2]);
                RADAR_SETTINGS.knockedNonVisibleColor = ColorRGB(radarColors[3].color[0], radarColors[3].color[1], radarColors[3].color[2]);
            }
        }
                
        nav_elements::CheckboxComponent(Lbl("radar.show_distance").c_str(), &RADAR_SETTINGS.showDistance, Desc("radar.show_distance", ICON_MS_STRAIGHTEN).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        
        // Additional radar settings can be added here
        const char* radarTypes[] = { "Circle", "Rectangle" };
        {
        int radarType = static_cast<int>(RADAR_SETTINGS.type);
        if (nav_elements::ComboEx(Lbl("radar.type").c_str(), &radarType, radarTypes, IM_ARRAYSIZE(radarTypes), Desc("radar.type", ICON_MS_RADAR).c_str())) {
            RADAR_SETTINGS.type = static_cast<RadarType>(radarType);
        }
    }
        
        nav_elements::SliderInt(Lbl("radar.max_distance").c_str(), &RADAR_SETTINGS.maxDistance, 1, 1000, Desc("radar.max_distance", nullptr).c_str(), "%d m", ICON_MS_STRAIGHTEN);
        nav_elements::Keybind(GET_TEXT("hotkeys.title").c_str(), "", &HOTKEY_SETTINGS.features.radarDistance.key, &HOTKEY_SETTINGS.features.radarDistance.isToggle);
    });

            // Radar position
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader(GET_TEXT("radar.position").c_str(), 0, 0);

    nav_elements::SliderInt(Lbl("radar.position_x").c_str(), &RADAR_SETTINGS.positionX, 0, 1920, Desc("radar.position_x", nullptr).c_str(), "%d px", ICON_MS_TRENDING_FLAT);
    nav_elements::SliderInt(Lbl("radar.position_y").c_str(), &RADAR_SETTINGS.positionY, 0, 1080, Desc("radar.position_y", nullptr).c_str(), "%d px", ICON_MS_TRENDING_FLAT);

    // Radar size
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader(GET_TEXT("radar.size").c_str(), 0, 0);

    if (RADAR_SETTINGS.type == RadarType::Circle) {
        nav_elements::SliderInt(Lbl("radar.circle_size").c_str(), &RADAR_SETTINGS.circleSize, 50, 300, Desc("radar.circle_size", nullptr).c_str(), "%d px", ICON_MS_ZOOM_IN);
    } else {
        nav_elements::SliderInt(Lbl("radar.rectangle_size").c_str(), &RADAR_SETTINGS.rectangleSize, 50, 400, Desc("radar.rectangle_size", nullptr).c_str(), "%d px", ICON_MS_ZOOM_IN);
    }

    // Player highlight options
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Player Highlight Options", 0, 0);

        nav_elements::CheckboxComponent(Lbl("radar.highlight_visible").c_str(), &RADAR_SETTINGS.useVisibleColor, Desc("radar.highlight_visible", ICON_MS_VISIBILITY).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("radar.highlight_closest").c_str(), &RADAR_SETTINGS.useClosestColor, Desc("radar.highlight_closest", ICON_MS_PERSON).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::CheckboxComponent(Lbl("radar.highlight_aiming_at_you").c_str(), &RADAR_SETTINGS.useAimingAtMeColor, Desc("radar.highlight_aiming_at_you", ICON_MS_CROSSHAIRS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);
        nav_elements::RenderExpandedContentAnimated(GET_TEXT("radar.highlight_visible"), []() {
            nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_radar_vis").c_str(), Desc("hotkeys.radar_highlight_visible", ICON_MS_VISIBILITY).c_str(), &HOTKEY_SETTINGS.features.radarVisibleColor.key, &HOTKEY_SETTINGS.features.radarVisibleColor.isToggle);
        });
        nav_elements::RenderExpandedContentAnimated(GET_TEXT("radar.highlight_closest"), []() {
            nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_radar_closest").c_str(), Desc("hotkeys.radar_highlight_closest", ICON_MS_PERSON).c_str(), &HOTKEY_SETTINGS.features.radarClosestColor.key, &HOTKEY_SETTINGS.features.radarClosestColor.isToggle);
        });
        nav_elements::RenderExpandedContentAnimated(GET_TEXT("radar.highlight_aiming_at_you"), []() {
            nav_elements::Keybind((std::string(GET_TEXT("hotkeys.title")) + "##kb_radar_aiming").c_str(), Desc("hotkeys.radar_highlight_aiming_at_you", ICON_MS_CROSSHAIRS).c_str(), &HOTKEY_SETTINGS.features.radarAimingAtMeColor.key, &HOTKEY_SETTINGS.features.radarAimingAtMeColor.isToggle);
        });

        // Radar colors
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Radar Colors", 0, 0);

 /*   if (AnimateNextFeature())
        nav_elements::ColorEdit("Visible Players", Settings.Colors.PlayerColors.RadarVisible, "Visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Non-Visible Players", Settings.Colors.PlayerColors.RadarNonVisible, "Non-visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Visible Players", Settings.Colors.PlayerColors.KnockedRadarVisible, "Knocked visible players color^" ICON_COLOR_FILL);
    if (AnimateNextFeature())
        nav_elements::ColorEdit("Knocked Non-Visible Players", Settings.Colors.PlayerColors.KnockedRadarNonVisible, "Knocked non-visible players color^" ICON_COLOR_FILL);*/
}

void CGui::RenderSettingsTab() {
    // Settings section
    //RenderSectionHeader("Settings", 0, 0);
    // Notification Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("Notification Settings", 0, 0);

        nav_elements::CheckboxComponent(Lbl("ui.notifications.enable").c_str(), &g_NotificationSettings.Enable, Desc("ui.notifications.enable", ICON_MS_NOTIFICATIONS).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

        nav_elements::SliderFloat(Lbl("ui.notification_duration").c_str(), &g_NotificationSettings.DisplayDuration, 1.0f, 20.0f, "How long notifications appear^" ICON_MS_TIMER, "%.1f s");

    // UI Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader("UI Settings", 0, 0);

    // Language selection
    const char* languages[] = { "English", "French", "German", "Russian", "Spanish", "Chinese" };
        {
        int lang = static_cast<int>(CONFIG_SETTINGS.language);
        if (nav_elements::ComboEx(Lbl("ui.language").c_str(), &lang, languages, IM_ARRAYSIZE(languages), Desc("ui.language", ICON_MS_TRANSLATE).c_str())) {
            CONFIG_SETTINGS.language = static_cast<Language>(lang);
        }
    }

    // Feature definitions/tooltips
        nav_elements::CheckboxComponent(Lbl("ui.show_feature_descriptions").c_str(), &CONFIG_SETTINGS.showFeatureDefinitions, Desc("ui.show_feature_descriptions", ICON_MS_INFO).c_str(), false, std::vector<nav_elements::ColorState>{}, nullptr, nullptr, false);

    // Font Settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader(GET_TEXT("ui.font_settings").c_str(), 0, 0);

    // Default font selection
    nav_elements::FontSelector(Lbl("fonts.default").c_str(), &Settings.Fonts.defaultFont, Desc("fonts.default", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat("Default Font Size", &Settings.Fonts.defaultFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    // Feature-specific font selections
    nav_elements::FontSelector(Lbl("fonts.player_esp").c_str(), &Settings.Fonts.playerEspFont, Desc("fonts.player_esp", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &Settings.Fonts.playerEspFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    nav_elements::FontSelector(Lbl("fonts.item_esp").c_str(), &Settings.Fonts.itemEspFont, Desc("fonts.item_esp", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &Settings.Fonts.itemEspFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    nav_elements::FontSelector(Lbl("fonts.radar").c_str(), &Settings.Fonts.radarFont, Desc("fonts.radar", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &Settings.Fonts.radarFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    nav_elements::FontSelector(Lbl("fonts.aimbot").c_str(), &Settings.Fonts.aimbotFont, Desc("fonts.aimbot", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &Settings.Fonts.aimbotFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    nav_elements::FontSelector(Lbl("fonts.ui").c_str(), &Settings.Fonts.uiFont, Desc("fonts.ui", ICON_MS_FONT_DOWNLOAD).c_str());
    nav_elements::SliderFloat(Lbl("visuals.font_size").c_str(), &Settings.Fonts.uiFontSize, 8.0f, 32.0f, Desc("visuals.font_size", ICON_MS_FORMAT_SIZE).c_str(), "%.1f");

    // Custom font management
    ImGui::Dummy(ImVec2(0, 10));
    RenderSectionHeader("Custom Font Management", 0, 0);

    // Load custom font button
    static char customFontPath[256] = "";
    static float customFontSize = 16.0f;
    
    ImGui::Text("Load Custom Font:");
    ImGui::SameLine();
    if (ImGui::Button("Browse Font File^" ICON_MS_FOLDER_OPEN, ImVec2(120, 25))) {
        // Open file dialog for font selection
        OPENFILENAMEA ofn;
        ZeroMemory(&ofn, sizeof(ofn));
        ofn.lStructSize = sizeof(ofn);
        ofn.hwndOwner = NULL;
        ofn.lpstrFilter = "Font Files\0*.ttf;*.otf\0All Files\0*.*\0";
        ofn.lpstrFile = customFontPath;
        ofn.nMaxFile = sizeof(customFontPath);
        ofn.Flags = OFN_PATHMUSTEXIST | OFN_FILEMUSTEXIST;
        
        if (GetOpenFileNameA(&ofn)) {
            // Font file selected
        }
    }
    
    if (strlen(customFontPath) > 0) {
        ImGui::SameLine();
        ImGui::Text("Selected: %s", customFontPath);
        
        ImGui::Text("Font Size:");
        ImGui::SameLine();
        ImGui::SliderFloat("##CustomFontSize", &customFontSize, 8.0f, 32.0f, "%.1f");
        
        ImGui::SameLine();
        if (ImGui::Button("Load Font^" ICON_MS_DOWNLOAD, ImVec2(80, 25))) {
            if (g_FontSystem.LoadCustomFont(customFontPath, customFontSize)) {
                // Clear the path after successful load
                memset(customFontPath, 0, sizeof(customFontPath));
                customFontSize = 16.0f;
            } else {
                // Show error message
                ImGui::OpenPopup("Font Load Error");
            }
        }
    }

    // Custom fonts list
    const auto& customFonts = g_FontSystem.GetCustomFonts();
    if (!customFonts.empty()) {
        ImGui::Dummy(ImVec2(0, 10));
        ImGui::Text("Loaded Custom Fonts:");
        
        for (const auto& font : customFonts) {
            ImGui::Text("• %s (%.1fpt)", font.name.c_str(), font.size);
            ImGui::SameLine();
            if (ImGui::Button(("Remove##" + font.name).c_str(), ImVec2(60, 20))) {
                g_FontSystem.UnloadCustomFont(font.name);
            }
        }
    }

    // Animation settings
    ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader(GET_TEXT("ui.animation_settings").c_str(), 0, 0);

        nav_elements::SliderFloat(Lbl("ui.animation_speed").c_str(), &animationSpeed, 0.01f, 0.3f, Desc("ui.animation_speed", ICON_MS_SPEED).c_str(), "%.2f");

        // About section
        ImGui::Dummy(ImVec2(0, 15)); // Add more space between sections
    RenderSectionHeader(GET_TEXT("ui.about").c_str(), 0, 0);

    ImGui::Text("BitCheats Fortnite v4.2.1");
    ImGui::Text("© 2025 BitCheats Team");
}

// Forward declare RenderConfigsTab function

void CGui::RenderConfigsTab() {
    // Match CheckboxComponent styling exactly - consistent 10px padding
    ImGui::Dummy(ImVec2(0, 10));

    //// Title section matching hotkey system UI design
    //ImGui::PushFont(iconsBig);
    //ImGui::Text(ICON_MS_FOLDER);
    //ImGui::PopFont();
    //ImGui::SameLine();
    //ImGui::Text("Configuration Management");

    //ImGui::Dummy(ImVec2(0, 8));

    //// Visual separator matching CheckboxComponent style
    //pGUi.RenderGradientSeparator(0, 0.75f);

    //// Description section with consistent spacing
    //ImGui::Dummy(ImVec2(0, 5));
    //ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Manage your game configurations - create, share, and load different settings");

    //ImGui::Dummy(ImVec2(0, 15)); // Double padding as requested

    // Search bar with CheckboxComponent styling
    static char searchBuffer[128] = "";
    ImGui::Dummy(ImVec2(0, 5));

    // Match CheckboxComponent frame padding
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 10)); // Consistent 10px padding

    // Get current window and draw list
    ImGuiWindow* window = ImGui::GetCurrentWindow();
    ImDrawList* drawList = window->DrawList;

    // Set up search bar with CheckboxComponent width proportions
    float searchBarWidth = ImGui::GetContentRegionAvail().x * 0.35f;
    ImGui::SetNextItemWidth(searchBarWidth);

    // Get position and size for custom drawing matching CheckboxComponent
    ImVec2 pos = ImGui::GetCursorScreenPos();
    ImVec2 size = ImVec2(searchBarWidth, 42.0f); // Match CheckboxComponent height
    ImRect rect(pos, ImVec2(pos.x + size.x, pos.y + size.y));

    // Animation matching CheckboxComponent exactly
    struct SearchBarAnimation {
        float element_opacity = 0.0f;
        float line_opacity = 0.0f;
        ImColor border_color = ImColor(255, 255, 255, 10);
    };

    static std::map<ImGuiID, SearchBarAnimation> search_anim_map;
    ImGuiID id = window->GetID("##ConfigSearch");
    bool hovered = ImGui::IsMouseHoveringRect(rect.Min, rect.Max);
    bool active = ImGui::IsItemActive();

    auto it_anim = search_anim_map.find(id);
    if (it_anim == search_anim_map.end()) {
        search_anim_map.insert({id, {}});
        it_anim = search_anim_map.find(id);
    }

    // Animation speed matching CheckboxComponent
    const float anim_speed = 0.14f; // Match CheckboxComponent animation speed

    // Background animation matching CheckboxComponent
    it_anim->second.element_opacity = ImLerp(it_anim->second.element_opacity,
                                           (active ? 0.08f : hovered ? 0.04f : 0.02f),
                                           anim_speed * (1.0f - ImGui::GetIO().DeltaTime));

    // Line animation matching CheckboxComponent
    it_anim->second.line_opacity = ImLerp(it_anim->second.line_opacity,
                                        (active || hovered) ? 1.0f : 0.0f,
                                        0.07f * (1.0f - ImGui::GetIO().DeltaTime));

    // Border color animation matching CheckboxComponent
    ImVec4 current_color = ImGui::ColorConvertU32ToFloat4(it_anim->second.border_color);
    ImVec4 target_color = ImGui::ColorConvertU32ToFloat4(active || hovered ? gui.checkboxstrokeactive : gui.checkboxstroke);
    ImVec4 lerped_color = ImLerp(current_color, target_color, anim_speed * (1.0f - ImGui::GetIO().DeltaTime));
    it_anim->second.border_color = ImGui::ColorConvertFloat4ToU32(lerped_color);

    // Draw background matching CheckboxComponent exactly
    ImColor backgroundColor = func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f);
    drawList->AddRectFilled(rect.Min, rect.Max, backgroundColor, 8.0f); // 8-pixel rounded corners

    // Draw animated glow matching CheckboxComponent
    drawList->AddRectFilled(rect.Min, rect.Max, ImColor(1.0f, 1.0f, 1.0f, it_anim->second.element_opacity), 8.0f);

    // Draw border with glowing effect matching CheckboxComponent
    drawList->AddRect(rect.Min, rect.Max, it_anim->second.border_color, 8.0f, 0, 1.0f);

    // Draw bottom line with animation matching CheckboxComponent
    if (it_anim->second.line_opacity > 0.01f) {
        const float lineHeight = 2.0f;
        const float linePadding = 8.0f; // Match CheckboxComponent padding
        ImVec2 lineStart(rect.Min.x + linePadding, rect.Max.y - lineHeight - 2.0f);
        ImVec2 lineEnd(rect.Max.x - linePadding, lineStart.y + lineHeight);

        // Shadow opacity matching CheckboxComponent
        float shadowOpacity = active ? 1.0f : 0.6f;

        // Shadow for horizontal line matching CheckboxComponent
        drawList->AddShadowRect(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * it_anim->second.line_opacity),
            15.f,
            ImVec2(0, 2),
            ImDrawFlags_RoundCornersAll,
            40.f
        );

        // Main glow line matching CheckboxComponent
        drawList->AddRectFilled(
            lineStart,
            lineEnd,
            ImColor(accent_color[2], accent_color[1], accent_color[0], it_anim->second.line_opacity),
            8.0f, // Match rounded corners
            ImDrawFlags_RoundCornersAll
        );
    }

    // Input text with transparent background
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
    if (ImGui::InputTextWithHint("##ConfigSearch", "Search by name or config ID", searchBuffer, IM_ARRAYSIZE(searchBuffer))) {
        g_ConfigSystem.searchQuery = searchBuffer;
    }

    ImGui::SameLine();

    // Search button with CheckboxComponent styling (42px height to match)
    if (nav_elements::IconButton(GET_TEXT("ui.search").c_str(), ICON_MS_SEARCH, ImVec2(120, 42), ImColor(gui.main))) {
        // Search functionality is handled by input text already
    }

    // Position the Create New Config button with proper spacing
    float windowWidth = ImGui::GetContentRegionAvail().x;
    ImGui::SameLine(windowWidth - 180); // Consistent positioning

    // Create New Config button with robust popup handling
    if (nav_elements::IconButton(GET_TEXT("configs.new").c_str(), ICON_MS_ADD_CIRCLE, ImVec2(170, 42), ImColor(gui.main))) {
        // Initialize new config popup
        if (!gui.picker_active) {
            gui.picker_active = true;
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = "";
            g_ConfigSystem.shareConfigType = "";
        }
    }

    ImGui::PopStyleColor();
    ImGui::PopStyleVar();

    ImGui::Dummy(ImVec2(0, 10));

    // Refresh current configs
    static bool initialized = false;
    if (!initialized) {
        InitializeConfigSystem();
        initialized = true;
    }

    ImGui::Dummy(ImVec2(0, 5));

    // Config entry containers matching CheckboxComponent styling
    const float entryHeight = 75.0f; // Match CheckboxComponent proportions
    const float windowWidth2 = ImGui::GetContentRegionAvail().x;
    const float entryPadding = 10.0f; // Consistent 10px padding as requested

    // Empty state message with consistent styling
    if (g_ConfigSystem.configs.empty()) {
        ImGui::Dummy(ImVec2(0, 20)); // Double padding
        ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "No configurations found. Create one to get started.");
        return; // Early return to prevent further processing
    }

    // Animation timing matching CheckboxComponent
    static float animation_time = 0.0f;
    animation_time += ImGui::GetIO().DeltaTime;
    float pulse = (sinf(animation_time * 2.5f) * 0.5f + 0.5f) * 0.3f + 0.7f;

    // Display config entries
    for (int i = 0; i < g_ConfigSystem.configs.size(); i++) {
        const ConfigFile& config = g_ConfigSystem.configs[i];

        // Filter by search query if exists
        if (!g_ConfigSystem.searchQuery.empty()) {
            std::string lowerName = config.name;
            std::string lowerType = config.type;
            std::string lowerSearch = g_ConfigSystem.searchQuery;

            // Get config ID for search
            std::string configId = config.filename;
            size_t dotPos = configId.find_last_of('.');
            if (dotPos != std::string::npos) {
                configId = configId.substr(0, dotPos);
            }
            std::string lowerConfigId = configId;

            // Convert to lowercase for case-insensitive comparison
            std::transform(lowerName.begin(), lowerName.end(), lowerName.begin(), ::tolower);
            std::transform(lowerType.begin(), lowerType.end(), lowerType.begin(), ::tolower);
            std::transform(lowerConfigId.begin(), lowerConfigId.end(), lowerConfigId.begin(), ::tolower);
            std::transform(lowerSearch.begin(), lowerSearch.end(), lowerSearch.begin(), ::tolower);

            if (lowerName.find(lowerSearch) == std::string::npos &&
                lowerType.find(lowerSearch) == std::string::npos &&
                lowerConfigId.find(lowerSearch) == std::string::npos) {
                continue; // Skip if not matching search
            }
        }

        // Create a unique ID for each config entry
        ImGui::PushID(i);

        // Config entry background
        ImVec2 entryPos = ImGui::GetCursorScreenPos();
        ImVec2 entrySize(windowWidth2, entryHeight);
        ImDrawList* drawList = ImGui::GetWindowDrawList();

        // Get if entry is hovered
        bool entryHovered = ImGui::IsMouseHoveringRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y)
        );

        // Animation structure matching CheckboxComponent exactly
        struct ConfigEntryAnimation {
            float element_opacity = 0.0f;
            float line_opacity = 0.0f;
            ImColor border_color = ImColor(255, 255, 255, 10);
        };

        static std::map<ImGuiID, ConfigEntryAnimation> config_anim_map;
        ImGuiID configId = window->GetID(("##ConfigEntry" + std::to_string(i)).c_str());

        auto config_anim = config_anim_map.find(configId);
        if (config_anim == config_anim_map.end()) {
            config_anim_map.insert({configId, {}});
            config_anim = config_anim_map.find(configId);
        }

        // Animation speed matching CheckboxComponent exactly
        const float anim_speed = 0.14f;

        // Background animation matching CheckboxComponent
        config_anim->second.element_opacity = ImLerp(config_anim->second.element_opacity,
                                                  (config.isSelected ? 0.08f : entryHovered ? 0.04f : 0.02f),
                                                  anim_speed * (1.0f - ImGui::GetIO().DeltaTime));

        // Line animation matching CheckboxComponent
        config_anim->second.line_opacity = ImLerp(config_anim->second.line_opacity,
                                               (config.isSelected || entryHovered) ? 1.0f : 0.0f,
                                               0.07f * (1.0f - ImGui::GetIO().DeltaTime));

        // Border color animation matching CheckboxComponent
        ImVec4 current_color = ImGui::ColorConvertU32ToFloat4(config_anim->second.border_color);
        ImVec4 target_color = ImGui::ColorConvertU32ToFloat4(entryHovered ? gui.checkboxstrokeactive : gui.checkboxstroke);
        ImVec4 lerped_color = ImLerp(current_color, target_color, anim_speed * (1.0f - ImGui::GetIO().DeltaTime));
        config_anim->second.border_color = ImGui::ColorConvertFloat4ToU32(lerped_color);

        // Draw background matching CheckboxComponent exactly
        ImColor backgroundColor = func.GetColorWithAlpha(ImColor(23, 17, 13, 155), 0.3f);
        drawList->AddRectFilled(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            backgroundColor,
            8.0f // 8-pixel rounded corners matching CheckboxComponent
        );

        // Draw animated glow matching CheckboxComponent
        drawList->AddRectFilled(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            ImColor(1.0f, 1.0f, 1.0f, config_anim->second.element_opacity),
            8.0f
        );

        // Draw border matching CheckboxComponent
        ImColor borderColor = entryHovered ?
            gui.checkboxstrokeactive : func.GetColorWithAlpha(gui.checkboxstroke, 0.3f);
        drawList->AddRect(
            entryPos,
            ImVec2(entryPos.x + entrySize.x, entryPos.y + entrySize.y),
            borderColor,
            8.0f, 0, 1.0f
        );

        // Draw bottom line matching CheckboxComponent
        if (config_anim->second.line_opacity > 0.01f) {
            const float lineHeight = 2.0f;
            const float linePadding = 8.0f; // Match CheckboxComponent padding
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Shadow opacity matching CheckboxComponent
            float shadowOpacity = config.isSelected ? 1.0f : 0.6f;

            // Shadow for horizontal line matching CheckboxComponent
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], shadowOpacity * config_anim->second.line_opacity),
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line matching CheckboxComponent
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                ImColor(accent_color[2], accent_color[1], accent_color[0], config_anim->second.line_opacity),
                8.0f, // Match rounded corners
                ImDrawFlags_RoundCornersAll
            );
        }

        // Selected config accent line matching CheckboxComponent
        if (config.isSelected) {
            const float lineHeight = 2.0f;
            const float linePadding = 8.0f; // Match CheckboxComponent padding
            ImVec2 lineStart(entryPos.x + linePadding, entryPos.y + entrySize.y - lineHeight - 2.0f);
            ImVec2 lineEnd(entryPos.x + entrySize.x - linePadding, lineStart.y + lineHeight);

            // Blue glow effect matching CheckboxComponent
            ImColor lineColor(accent_color[2], accent_color[1], accent_color[0], 0.8f * pulse);

            // Shadow for horizontal line matching CheckboxComponent
            drawList->AddShadowRect(
                lineStart,
                lineEnd,
                lineColor,
                15.f,
                ImVec2(0, 2),
                ImDrawFlags_RoundCornersAll,
                40.f
            );

            // Main glow line matching CheckboxComponent
            drawList->AddRectFilled(
                lineStart,
                lineEnd,
                lineColor,
                8.0f, // Match rounded corners
                ImDrawFlags_RoundCornersAll
            );
        }

        // Responsive 3-column button grid layout
        float iconButtonSize = 36.0f;    // Square icon-only buttons
        float buttonHeight = 36.0f;
        float padding = 8.0f;             // Spacing between buttons
        float buttonY = entryPos.y + (entryHeight - buttonHeight) * 0.5f;

        // Layout with consistent 10px spacing matching CheckboxComponent
        float col1_x = entryPos.x + 15;   // Name column
        float col2_x = entryPos.x + 155;  // Type column
        float col3_x = entryPos.x + 235;  // ID column
        float col4_x = entryPos.x + 315;  // Creator column
        float col5_x = entryPos.x + 435;  // Modified At column

        // Column widths with proper spacing
        float col1_width = 135;  // Name column width
        float col2_width = 75;   // Type column width
        float col3_width = 75;   // ID column width
        float col4_width = 115;  // Creator column width
        float col5_width = 90;   // Modified At column width

        // Responsive button area - 3 equal columns
        float buttonAreaStart = col5_x + col5_width + 15; // Start after text columns
        float availableButtonWidth = entrySize.x - (buttonAreaStart - entryPos.x) - 15; // Available width for buttons
        float buttonColumnWidth = (availableButtonWidth - padding * 2) / 3.0f; // 3 equal columns with padding

        // Base Y positions for consistent alignment
        float baseY = entryPos.y + 12;
        float secondLineY = entryPos.y + 32;

        // === COLUMN 1: CONFIG NAME ===
        // Show label at top
        ImGui::SetCursorScreenPos(ImVec2(col1_x, baseY));
        ImGui::PushTextWrapPos(col1_x + col1_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Config Name:");
        ImGui::PopTextWrapPos();

        // Show config name at bottom with truncation (always visible, not just when selected)
        ImGui::SetCursorScreenPos(ImVec2(col1_x, secondLineY));
        ImGui::PushTextWrapPos(col1_x + col1_width); // Prevent text overflow

        // Truncate config name if longer than 12 characters
        std::string displayName = config.name;
        if (displayName.length() > 12) {
            displayName = displayName.substr(0, 12) + "...";
        }

        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", displayName.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 2: CONFIG TYPE ===
        ImGui::SetCursorScreenPos(ImVec2(col2_x, baseY));
        ImGui::PushTextWrapPos(col2_x + col2_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Type:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col2_x, secondLineY));
        ImGui::PushTextWrapPos(col2_x + col2_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", config.type.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 3: CONFIG ID ===
        // Extract ID from filename (6-digit random ID for config identification)
        std::string configIdStr = config.filename;
        size_t dotPos = configIdStr.find_last_of('.');
        if (dotPos != std::string::npos && configIdStr.substr(dotPos) == ".json") {
            configIdStr = configIdStr.substr(0, dotPos);
        }

        // Truncate ID if longer than 12 characters (though typically 6-digit numbers)
        if (configIdStr.length() > 12) {
            configIdStr = configIdStr.substr(0, 12) + "...";
        }

        ImGui::SetCursorScreenPos(ImVec2(col3_x, baseY));
        ImGui::PushTextWrapPos(col3_x + col3_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "ID:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col3_x, secondLineY));
        ImGui::PushTextWrapPos(col3_x + col3_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", configIdStr.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 4: CREATOR ===
        const char* creatorName = config.isDefault ? "BitCheats" : "Creator";
        ImGui::SetCursorScreenPos(ImVec2(col4_x, baseY));
        ImGui::PushTextWrapPos(col4_x + col4_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Creator:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col4_x, secondLineY));
        ImGui::PushTextWrapPos(col4_x + col4_width); // Prevent text overflow

        // Truncate creator name if longer than 12 characters
        std::string displayCreator = creatorName;
        if (displayCreator.length() > 12) {
            displayCreator = displayCreator.substr(0, 12) + "...";
        }

        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "@%s", displayCreator.c_str());
        ImGui::PopTextWrapPos();

        // === COLUMN 5: MODIFIED DATE ===
        char timeBuffer[64];
        std::time_t time = std::chrono::system_clock::to_time_t(config.lastModified);
        std::tm tm;
        localtime_s(&tm, &time);
        std::strftime(timeBuffer, sizeof(timeBuffer), "%m/%d %H:%M", &tm);

        ImGui::SetCursorScreenPos(ImVec2(col5_x, baseY));
        ImGui::PushTextWrapPos(col5_x + col5_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.6f, 0.6f, 0.6f, 1.0f), "Modified:");
        ImGui::PopTextWrapPos();
        ImGui::SetCursorScreenPos(ImVec2(col5_x, secondLineY));
        ImGui::PushTextWrapPos(col5_x + col5_width); // Prevent text overflow
        ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "%s", timeBuffer);
        ImGui::PopTextWrapPos();



        // Add clickable region for the left part of the entry only - not overlapping with buttons
        float clickableWidth = buttonAreaStart - entryPos.x - 10; // Up to button area start with margin

        // Save current cursor position
        ImVec2 originalCursorPos = ImGui::GetCursorScreenPos();

        // Set cursor to the beginning of the entry
        ImGui::SetCursorScreenPos(entryPos);

        // Create invisible button that only covers the left part of the entry
        ImGui::InvisibleButton("##ConfigEntryBackground", ImVec2(clickableWidth, entrySize.y));

        // Check if the entry is clicked to select the config
        if (ImGui::IsItemClicked()) {
            // Select this config
            SelectConfiguration(i);
        }

        // Restore cursor position for the buttons
        ImGui::SetCursorScreenPos(originalCursorPos);

        // Responsive 3-column button grid
        // Column 1: Delete button (leftmost)
        ImVec2 deleteButtonPos = ImVec2(buttonAreaStart, buttonY);
        ImGui::SetCursorScreenPos(deleteButtonPos);

        // Delete button with same design as share button but crimson hover
        static int configToDelete = -1;
        static bool showDeleteConfirmation = false;

        // Custom delete button with crimson hover (matching share button design)
        ImGui::SetCursorScreenPos(deleteButtonPos);
        ImGui::PushID(("delete_" + std::to_string(i)).c_str());

        // Create invisible button for interaction
        bool deletePressed = ImGui::InvisibleButton("##delete", ImVec2(buttonColumnWidth, buttonHeight));
        bool deleteHovered = ImGui::IsItemHovered();

        // Get button rect
        ImRect deleteRect = ImRect(deleteButtonPos, ImVec2(deleteButtonPos.x + buttonColumnWidth, deleteButtonPos.y + buttonHeight));

        // Animation state for delete button (matching IconOnlyButton design)
        static std::map<int, float> deleteElementOpacity;
        static std::map<int, ImVec4> deleteIconColor;

        if (deleteElementOpacity.find(i) == deleteElementOpacity.end()) {
            deleteElementOpacity[i] = 0.0f;
            deleteIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        // Fast animation system for immediate response
        deleteElementOpacity[i] = ImLerp(deleteElementOpacity[i],
            deleteHovered ? 0.01f : 0.0f,
            0.5f); // Much faster animation

        // Immediate color change for crimson hover (no animation)
        ImVec4 targetIconColor = deleteHovered ?
            ImVec4(220.0f/255.0f, 20.0f/255.0f, 60.0f/255.0f, 1.0f) : // Crimson on hover
            ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Default color

        deleteIconColor[i] = targetIconColor; // Immediate color change, no animation

        // Draw background exactly like IconOnlyButton
        drawList->AddRectFilled(deleteRect.Min, deleteRect.Max,
            ImColor(1.0f, 1.0f, 1.0f, deleteElementOpacity[i]), 3.0f);

        // Draw border exactly like IconOnlyButton
        ImVec4 border_color = deleteHovered ?
            ImVec4(ImColor(255, 255, 255, 10)) :
            ImVec4(ImColor(255, 255, 255, 10));
        drawList->AddRect(deleteRect.Min, deleteRect.Max,
            ImGui::ColorConvertFloat4ToU32(border_color), 3.0f, 0, 1.0f);

        // Draw icon shadow exactly like IconOnlyButton
        ImVec2 deleteIconCenter = deleteRect.GetCenter();
        drawList->AddShadowCircle(
            deleteIconCenter,
            9.f,
            ImGui::ColorConvertFloat4ToU32(deleteIconColor[i]),
            40,
            ImVec2(0, 0),
            0,
            360
        );

        // Draw delete icon centered
        ImGui::PushFont(iconsBig);
        ImVec2 deleteIconSize = ImGui::CalcTextSize(ICON_MS_DELETE);
        const float delete_icon_scale = 0.8f; // Same scale as IconOnlyButton
        ImVec2 deleteIconPos = ImVec2(
            deleteIconCenter.x - (deleteIconSize.x * delete_icon_scale) * 0.5f,
            deleteIconCenter.y - (deleteIconSize.y * delete_icon_scale) * 0.5f
        );

        ImGui::SetWindowFontScale(delete_icon_scale);
        drawList->AddText(deleteIconPos, ImGui::ColorConvertFloat4ToU32(deleteIconColor[i]), ICON_MS_DELETE);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();

        ImGui::PopID();

        if (deletePressed && !config.isDefault) {
            configToDelete = i;
            showDeleteConfirmation = true;
        }

        // Column 2: Share button (middle) - with fast animation
        ImVec2 shareButtonPos = ImVec2(buttonAreaStart + buttonColumnWidth + padding, buttonY);
        ImGui::SetCursorScreenPos(shareButtonPos);
        ImGui::PushID(("share_" + std::to_string(i)).c_str());

        bool sharePressed = ImGui::InvisibleButton("##share", ImVec2(buttonColumnWidth, buttonHeight));
        bool shareHovered = ImGui::IsItemHovered();

        // Fast animation for share button
        static std::map<int, float> shareElementOpacity;
        static std::map<int, ImVec4> shareIconColor;

        if (shareElementOpacity.find(i) == shareElementOpacity.end()) {
            shareElementOpacity[i] = 0.0f;
            shareIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        shareElementOpacity[i] = ImLerp(shareElementOpacity[i], shareHovered ? 0.01f : 0.0f, 0.5f);
        shareIconColor[i] = shareHovered ?
            ImVec4(0.9f, 0.2f, 0.2f, 1.0f) : // Red on hover
            ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Default color (immediate change)

        // Draw share button
        ImRect shareRect = ImRect(shareButtonPos, ImVec2(shareButtonPos.x + buttonColumnWidth, shareButtonPos.y + buttonHeight));
        drawList->AddRectFilled(shareRect.Min, shareRect.Max, ImColor(1.0f, 1.0f, 1.0f, shareElementOpacity[i]), 3.0f);
        drawList->AddRect(shareRect.Min, shareRect.Max, ImColor(255, 255, 255, 10), 3.0f, 0, 1.0f);

        ImVec2 shareIconCenter = shareRect.GetCenter();
        drawList->AddShadowCircle(shareIconCenter, 9.f, ImGui::ColorConvertFloat4ToU32(shareIconColor[i]), 40, ImVec2(0, 0), 0, 360);

        ImGui::PushFont(iconsBig);
        ImVec2 shareIconSize = ImGui::CalcTextSize(ICON_MS_SHARE);
        const float share_icon_scale = 0.8f;
        ImVec2 shareIconPos = ImVec2(
            shareIconCenter.x - (shareIconSize.x * share_icon_scale) * 0.5f,
            shareIconCenter.y - (shareIconSize.y * share_icon_scale) * 0.5f
        );
        ImGui::SetWindowFontScale(share_icon_scale);
        drawList->AddText(shareIconPos, ImGui::ColorConvertFloat4ToU32(shareIconColor[i]), ICON_MS_SHARE);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();
        ImGui::PopID();

        if (sharePressed) {
            g_ConfigSystem.showSharePopup = true;
            g_ConfigSystem.shareConfigName = config.name + "_Shared";
            g_ConfigSystem.shareConfigType = config.type;
        }

        // Column 3: Download/Load/Selected button (rightmost) - with fast animation
        ImVec2 actionButtonPos = ImVec2(buttonAreaStart + (buttonColumnWidth + padding) * 2, buttonY);
        ImGui::SetCursorScreenPos(actionButtonPos);
        ImGui::PushID(("action_" + std::to_string(i)).c_str());

        bool isCurrentlyLoaded = config.isSelected;
        const char* actionIcon;
        ImVec4 actionColor;

        if (isCurrentlyLoaded) {
            actionIcon = ICON_MS_CHECK_CIRCLE;
            actionColor = ImVec4(0.2f, 0.8f, 0.2f, 1.0f); // Green for selected
        } else if (config.isDownloaded) {
            actionIcon = ICON_MS_PLAY_ARROW;
            actionColor = ImVec4(0.2f, 0.8f, 0.2f, 1.0f); // Green for load
        } else {
            actionIcon = ICON_MS_DOWNLOAD;
            actionColor = ImVec4(0.2f, 0.6f, 1.0f, 1.0f); // Blue for download
        }

        bool actionPressed = ImGui::InvisibleButton("##action", ImVec2(buttonColumnWidth, buttonHeight));
        bool actionHovered = ImGui::IsItemHovered();

        // Fast animation for action button
        static std::map<int, float> actionElementOpacity;
        static std::map<int, ImVec4> actionIconColor;

        if (actionElementOpacity.find(i) == actionElementOpacity.end()) {
            actionElementOpacity[i] = 0.0f;
            actionIconColor[i] = ImVec4(1.0f, 1.0f, 1.0f, 0.7f);
        }

        actionElementOpacity[i] = ImLerp(actionElementOpacity[i], actionHovered ? 0.01f : 0.0f, 0.5f);
        actionIconColor[i] = actionHovered ? actionColor : ImVec4(1.0f, 1.0f, 1.0f, 0.7f); // Immediate change

        // Draw action button
        ImRect actionRect = ImRect(actionButtonPos, ImVec2(actionButtonPos.x + buttonColumnWidth, actionButtonPos.y + buttonHeight));
        drawList->AddRectFilled(actionRect.Min, actionRect.Max, ImColor(1.0f, 1.0f, 1.0f, actionElementOpacity[i]), 3.0f);
        drawList->AddRect(actionRect.Min, actionRect.Max, ImColor(255, 255, 255, 10), 3.0f, 0, 1.0f);

        ImVec2 actionIconCenter = actionRect.GetCenter();
        drawList->AddShadowCircle(actionIconCenter, 9.f, ImGui::ColorConvertFloat4ToU32(actionIconColor[i]), 40, ImVec2(0, 0), 0, 360);

        ImGui::PushFont(iconsBig);
        ImVec2 actionIconSize = ImGui::CalcTextSize(actionIcon);
        const float action_icon_scale = 0.8f;
        ImVec2 actionIconPos = ImVec2(
            actionIconCenter.x - (actionIconSize.x * action_icon_scale) * 0.5f,
            actionIconCenter.y - (actionIconSize.y * action_icon_scale) * 0.5f
        );
        ImGui::SetWindowFontScale(action_icon_scale);
        drawList->AddText(actionIconPos, ImGui::ColorConvertFloat4ToU32(actionIconColor[i]), actionIcon);
        ImGui::SetWindowFontScale(1.0f);
        ImGui::PopFont();
        ImGui::PopID();

        if (actionPressed && !isCurrentlyLoaded) {
            if (config.isDownloaded) {
                SelectConfiguration(i);
            } else {
                ConfigFile& nonConstConfig = g_ConfigSystem.configs[i];
                nonConstConfig.isDownloaded = true;
                g_NotificationManager.AddChange("Configurations", "Config Downloaded", false, true);
            }
        }

        // Delete confirmation popup
        if (showDeleteConfirmation && configToDelete == i) {
            ImGui::OpenPopup("##DeleteConfirmation");
        }

        // Center the delete confirmation popup
        ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
        ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
        ImGui::SetNextWindowSize(ImVec2(0, 0), ImGuiCond_Always);

        // Removed full-screen overlay for cleaner UI

        if (ImGui::BeginPopup("##DeleteConfirmation", ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoScrollbar)) {
            // Styling matching other popups
            ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(20, 20));
            ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
            ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(10, 10));

            // Warning header
            ImGui::PushFont(iconsBig);
            ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), ICON_MS_WARNING);
            ImGui::PopFont();
            ImGui::SameLine();
            ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "Delete Configuration");

            ImGui::Separator();
            ImGui::Spacing();

            // Warning message
            ImGui::Text("Are you sure you want to delete this configuration?");
            ImGui::Spacing();
            ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "Config: %s", g_ConfigSystem.configs[configToDelete].name.c_str());
            ImGui::TextColored(ImVec4(1.0f, 0.6f, 0.6f, 1.0f), "This action cannot be undone!");

            ImGui::Spacing();
            ImGui::Separator();
            ImGui::Spacing();

            // Buttons
            float buttonWidth = 100.0f;
            float spacing = 20.0f;
            float totalWidth = buttonWidth * 2 + spacing;
            float buttonStartX = (ImGui::GetContentRegionAvail().x - totalWidth) * 0.5f;
            ImGui::SetCursorPosX(buttonStartX);

            // Delete button (crimson)
            if (nav_elements::IconButton(GET_TEXT("ui.delete").c_str(), ICON_MS_DELETE, ImVec2(buttonWidth, 40), ImColor(220, 20, 60))) {
                DeleteConfiguration(g_ConfigSystem.configs[configToDelete].filename);
                showDeleteConfirmation = false;
                configToDelete = -1;
                ImGui::CloseCurrentPopup();
            }

            ImGui::SameLine();

            // Cancel button
            if (nav_elements::IconButton(GET_TEXT("ui.cancel").c_str(), ICON_MS_CANCEL, ImVec2(buttonWidth, 40), ImColor(0.6f, 0.6f, 0.6f))) {
                showDeleteConfirmation = false;
                configToDelete = -1;
                ImGui::CloseCurrentPopup();
            }

            ImGui::PopStyleVar(3);
            ImGui::PopStyleColor();
            ImGui::EndPopup();
        } else if (showDeleteConfirmation && configToDelete == i) {
            // Reset if popup was closed externally
            showDeleteConfirmation = false;
            configToDelete = -1;
        }

        // Enhanced visual separator between config entries
        if (i < g_ConfigSystem.configs.size() - 1) { // Don't add separator after last entry
            ImVec2 separatorStart = ImVec2(entryPos.x + 15, entryPos.y + entryHeight + entryPadding);
            ImVec2 separatorEnd = ImVec2(entryPos.x + entrySize.x - 15, separatorStart.y + 1);

            // Draw gradient separator line for better visual separation
            drawList->AddRectFilledMultiColor(
                separatorStart,
                separatorEnd,
                ImColor(0.0f, 0.0f, 0.0f, 0.0f),     // Transparent left
                ImColor(0.4f, 0.4f, 0.4f, 0.3f),     // Visible center
                ImColor(0.4f, 0.4f, 0.4f, 0.3f),     // Visible center
                ImColor(0.0f, 0.0f, 0.0f, 0.0f)      // Transparent right
            );
        }

        // Move cursor for next entry with double padding
        ImGui::SetCursorScreenPos(ImVec2(entryPos.x, entryPos.y + entryHeight + entryPadding * 2));

        ImGui::PopID();
    }

    // Robust Config Popup System - Complete Rewrite
    CGui::RenderConfigPopup();



}

// Modern Config Popup System - Redesigned UI
void CGui::RenderConfigPopup() {
    // Enhanced popup state management
    struct ConfigPopupState {
        bool isInitialized = false;
        bool isOperationInProgress = false;
        char nameBuffer[128] = "";
        char typeBuffer[64] = "";
        bool hasError = false;
        std::string errorMessage = "";
        bool shouldClose = false;
        float lastInteractionTime = 0.0f;
        bool isShareMode = false; // Determines if this is create or share mode
    };

    static ConfigPopupState popupState;

    // Only proceed if popup should be shown
    if (!g_ConfigSystem.showSharePopup) {
        // Reset state when popup is not active
        if (popupState.isInitialized) {
            popupState = ConfigPopupState(); // Reset to default state
        }
        return;
    }

    // Initialize popup state on first show with crash protection
    if (!popupState.isInitialized) {
        // Prevent rapid popup creation with debouncing
        static float lastConfigPopupTime = 0.0f;
        float currentTime = ImGui::GetTime();
        
        if (lastConfigPopupTime == 0.0f || (currentTime - lastConfigPopupTime) > 0.3f) { // 300ms debounce
            // Determine if this is share mode (pre-filled data) or create mode (empty)
            popupState.isShareMode = !g_ConfigSystem.shareConfigName.empty() &&
                                     g_ConfigSystem.shareConfigName.find("_Shared") != std::string::npos;

            // Pre-fill from system state
            if (!g_ConfigSystem.shareConfigName.empty()) {
                strncpy_s(popupState.nameBuffer, g_ConfigSystem.shareConfigName.c_str(), sizeof(popupState.nameBuffer) - 1);
                popupState.nameBuffer[sizeof(popupState.nameBuffer) - 1] = '\0';
            }
            if (!g_ConfigSystem.shareConfigType.empty()) {
                strncpy_s(popupState.typeBuffer, g_ConfigSystem.shareConfigType.c_str(), sizeof(popupState.typeBuffer) - 1);
                popupState.typeBuffer[sizeof(popupState.typeBuffer) - 1] = '\0';
            }

            popupState.isInitialized = true;
            popupState.hasError = false;
            popupState.errorMessage = "";
            popupState.lastInteractionTime = ImGui::GetTime();

            // Open the popup with unique ID
            gui.darkoverlay = true;
            gui.picker_active = true;
            ImGui::OpenPopup("##ModernConfigPopup");
            
            lastConfigPopupTime = currentTime;
        }
    }

    // Center popup within main UI area
    ImVec2 center(gui.window_pos.x + gui.window_size.x * 0.5f, gui.window_pos.y + gui.window_size.y * 0.5f);
    ImGui::SetNextWindowPos(center, ImGuiCond_Always, ImVec2(0.5f, 0.5f));
    ImGui::SetNextWindowSize(ImVec2(0, 0), ImGuiCond_Always); // Auto-size

    // Removed full-screen overlay for cleaner UI

    // Begin popup with keybind-style design
    if (ImGui::BeginPopup("##ModernConfigPopup", ImGuiWindowFlags_NoMove | ImGuiWindowFlags_AlwaysAutoResize | ImGuiWindowFlags_NoScrollbar | ImGuiWindowFlags_NoTitleBar)) {
        // Styling matching keybind popup system
        ImGui::PushStyleColor(ImGuiCol_PopupBg, func.ImColorToImVec4(gui.window_bg));
        ImGui::PushStyleColor(ImGuiCol_Border, func.ImColorToImVec4(gui.stroke));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 3.0f); // Use fixed value like keybind popup
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 1.f);
        ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(10, 10)); // Consistent 10px spacing

        // Update interaction time when user interacts with popup
        if (ImGui::IsWindowHovered() || ImGui::IsAnyItemActive() || ImGui::IsAnyItemFocused()) {
            popupState.lastInteractionTime = ImGui::GetTime();
        }

        // Handle escape key (only close if not actively typing)
        if (ImGui::IsKeyPressed(ImGuiKey_Escape) && !ImGui::IsAnyItemActive()) {
            popupState.shouldClose = true;
        }

        // Handle clicking outside (only if not interacting with popup content)
        bool justOpened = false; static int& g_configPopupOpenFrame = *([](){ static int v=-1; return &v; })(); if (g_configPopupOpenFrame == -1) g_configPopupOpenFrame = ImGui::GetFrameCount(); justOpened = (ImGui::GetFrameCount() == g_configPopupOpenFrame);
        bool clicked_outside = !justOpened && ImGui::IsMouseClicked(ImGuiMouseButton_Left) &&
                              !ImGui::IsWindowHovered(ImGuiHoveredFlags_AnyWindow | ImGuiHoveredFlags_AllowWhenBlockedByPopup) &&
                              !ImGui::IsAnyItemHovered() && !ImGui::IsAnyItemActive();

        // Only close on outside click if user hasn't interacted recently (prevents accidental closure)
        if (clicked_outside && (ImGui::GetTime() - popupState.lastInteractionTime) > 0.5f) {
            popupState.shouldClose = true;
        }

        // Modern header with title and close button
        const char* title = popupState.isShareMode ? "Share Configuration" : "Create Configuration";
        const char* icon = popupState.isShareMode ? ICON_MS_SHARE : ICON_MS_ADD_CIRCLE;

        // Calculate header layout
        float headerHeight = 40.0f;
        float closeButtonSize = 24.0f;
        ImVec2 headerStart = ImGui::GetCursorPos();

        // Title with icon - left aligned with padding
        ImGui::SetCursorPos(ImVec2(headerStart.x + 10, headerStart.y + 8));
        ImGui::PushFont(iconsBig);
        ImGui::Text(icon);
        ImGui::PopFont();
        ImGui::SameLine();
        ImGui::Text(title);

        // Close button - top-right corner
        ImGui::SetCursorPos(ImVec2(headerStart.x + ImGui::GetContentRegionAvail().x - closeButtonSize - 10, headerStart.y + 8));

        // Custom close button with hover effect
        ImVec2 closeButtonPos = ImGui::GetCursorScreenPos();
        ImRect closeButtonRect(closeButtonPos, ImVec2(closeButtonPos.x + closeButtonSize, closeButtonPos.y + closeButtonSize));

        bool closeButtonHovered = ImGui::IsMouseHoveringRect(closeButtonRect.Min, closeButtonRect.Max);
        bool closeButtonClicked = closeButtonHovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left);

        // Draw close button background
        ImU32 closeButtonColor = closeButtonHovered ? ImColor(255, 60, 60, 180) : ImColor(120, 120, 120, 100);
        ImGui::GetWindowDrawList()->AddRectFilled(
            closeButtonRect.Min, closeButtonRect.Max,
            closeButtonColor, 4.0f
        );

        // Draw X icon
        ImGui::PushFont(iconsBig);
        ImVec2 closeIconSize = ImGui::CalcTextSize(ICON_MS_CLOSE);
        ImVec2 iconPos = ImVec2(
            closeButtonRect.Min.x + (closeButtonSize - closeIconSize.x) * 0.5f,
            closeButtonRect.Min.y + (closeButtonSize - closeIconSize.y) * 0.5f
        );
        ImGui::GetWindowDrawList()->AddText(iconPos, ImColor(255, 255, 255, 255), ICON_MS_CLOSE);
        ImGui::PopFont();

        if (closeButtonClicked) {
            popupState.shouldClose = true;
        }

        // Move cursor past header
        ImGui::SetCursorPos(ImVec2(headerStart.x, headerStart.y + headerHeight));
        ImGui::Separator();
        ImGui::Spacing();

        // Show error message if any
        if (popupState.hasError) {
            ImGui::PushStyleColor(ImGuiCol_Text, ImVec4(1.0f, 0.3f, 0.3f, 1.0f));
            ImGui::Text("Error: %s", popupState.errorMessage.c_str());
            ImGui::PopStyleColor();
            ImGui::Spacing();
        }

        // Modern input fields with identical styling
        const float inputWidth = 400.0f; // Fixed width for consistency
        const float inputHeight = 45.0f; // Slightly taller for modern look
        const float labelSpacing = 8.0f;
        const float fieldSpacing = 20.0f; // Space between fields

        // Helper function to render modern input field
        auto RenderModernInputField = [&](const char* label, const char* placeholder, char* buffer, size_t bufferSize, bool& changed) {
            // Label
            ImGui::Text(label);
            ImGui::Dummy(ImVec2(0, labelSpacing));

            // Center the input field
            float inputStartX = (ImGui::GetContentRegionAvail().x - inputWidth) * 0.5f;
            ImGui::SetCursorPosX(inputStartX);

            // Input field background and styling
            ImVec2 inputPos = ImGui::GetCursorScreenPos();
            ImVec2 inputSize = ImVec2(inputWidth, inputHeight);
            ImRect inputRect(inputPos, ImVec2(inputPos.x + inputSize.x, inputPos.y + inputSize.y));

            // Background matching current menu design
            ImGui::GetWindowDrawList()->AddRectFilled(
                inputRect.Min, inputRect.Max,
                func.GetColorWithAlpha(gui.background, 0.8f), // Match menu background
                8.0f
            );

            // Border matching current menu design
            bool isActive = ImGui::IsItemActive();
            bool isHovered = ImGui::IsItemHovered();
            ImU32 borderColor;

            if (isActive) {
                borderColor = gui.main; // Use accent color when active
            } else if (isHovered) {
                borderColor = func.GetColorWithAlpha(gui.main, 0.6f); // Dimmed accent on hover
            } else {
                borderColor = gui.stroke; // Default stroke color
            }

            ImGui::GetWindowDrawList()->AddRect(
                inputRect.Min, inputRect.Max,
                borderColor, 8.0f, 0, isActive ? 2.0f : 1.0f
            );

            // Input field styling matching current menu design
            ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.0f, 0.0f, 0.0f, 0.0f)); // Transparent (we draw custom background)
            ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
            ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.0f, 0.0f, 0.0f, 0.0f));
            ImGui::PushStyleColor(ImGuiCol_Text, func.ImColorToImVec4(gui.text[0])); // Use menu text color
            ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(15, 12)); // Consistent padding
            ImGui::PushStyleVar(ImGuiStyleVar_FrameRounding, 8.0f); // Match menu rounding

            ImGui::SetNextItemWidth(inputSize.x);
            changed = ImGui::InputTextWithHint(("##" + std::string(label)).c_str(), placeholder, buffer, bufferSize);

            ImGui::PopStyleVar(2);
            ImGui::PopStyleColor(4); // Updated to match the 4 style colors we pushed

            ImGui::Dummy(ImVec2(0, fieldSpacing));
        };

        // Configuration Name Field
        bool nameChanged = false;
        RenderModernInputField("Configuration Name", "Enter a name...",
                              popupState.nameBuffer, IM_ARRAYSIZE(popupState.nameBuffer), nameChanged);

        // Configuration Type Field
        bool typeChanged = false;
        RenderModernInputField("Configuration Type", "e.g., Rage, Legit, Balanced...",
                              popupState.typeBuffer, IM_ARRAYSIZE(popupState.typeBuffer), typeChanged);

        // Validate input in real-time
        if (nameChanged || typeChanged) {
            popupState.hasError = false;
            popupState.errorMessage = "";
            popupState.lastInteractionTime = ImGui::GetTime();

            if (strlen(popupState.nameBuffer) == 0) {
                popupState.hasError = true;
                popupState.errorMessage = "Configuration name cannot be empty";
            } else {
                std::string name = popupState.nameBuffer;
                if (name.find_first_of("<>:\"/\\|?*") != std::string::npos) {
                    popupState.hasError = true;
                    popupState.errorMessage = "Configuration name contains invalid characters";
                }
            }
        }

        ImGui::Spacing();
        ImGui::Separator();
        ImGui::Spacing();

        // Modern action button - single centered button
        const char* buttonText = popupState.isShareMode ? "Share Config" : "Save Config";
        const char* buttonIcon = popupState.isShareMode ? ICON_MS_SHARE : ICON_MS_SAVE;
        const float buttonWidth = 180.0f;
        const float buttonHeight = 50.0f;

        // Center the button
        float buttonStartX = (ImGui::GetContentRegionAvail().x - buttonWidth) * 0.5f;
        ImGui::SetCursorPosX(buttonStartX);

        bool canCreate = !popupState.hasError && strlen(popupState.nameBuffer) > 0 && !popupState.isOperationInProgress;

        // Custom modern button with perfect centering
        ImVec2 buttonPos = ImGui::GetCursorScreenPos();
        ImRect buttonRect(buttonPos, ImVec2(buttonPos.x + buttonWidth, buttonPos.y + buttonHeight));

        bool buttonHovered = ImGui::IsMouseHoveringRect(buttonRect.Min, buttonRect.Max) && canCreate;
        bool buttonClicked = buttonHovered && ImGui::IsMouseClicked(ImGuiMouseButton_Left);

        // Button background with modern gradient
        ImU32 buttonColor = canCreate ?
            (buttonHovered ? ImColor(80, 150, 255, 255) : ImColor(70, 130, 255, 255)) :
            ImColor(60, 60, 70, 255);

        ImGui::GetWindowDrawList()->AddRectFilled(
            buttonRect.Min, buttonRect.Max,
            buttonColor, 8.0f
        );

        // Button border
        ImGui::GetWindowDrawList()->AddRect(
            buttonRect.Min, buttonRect.Max,
            ImColor(90, 160, 255, 255), 8.0f, 0, 1.0f
        );

        // Calculate perfect centering for icon and text
        ImGui::PushFont(iconsBig);
        ImVec2 buttonIconSize = ImGui::CalcTextSize(buttonIcon);
        ImGui::PopFont();
        ImVec2 textSize = ImGui::CalcTextSize(buttonText);

        float totalContentWidth = buttonIconSize.x + 8.0f + textSize.x; // Icon + spacing + text
        float contentStartX = buttonRect.Min.x + (buttonWidth - totalContentWidth) * 0.5f;
        float contentY = buttonRect.Min.y + (buttonHeight - ImMax(buttonIconSize.y, textSize.y)) * 0.5f;

        // Draw icon
        ImGui::PushFont(iconsBig);
        ImGui::GetWindowDrawList()->AddText(
            ImVec2(contentStartX, contentY),
            canCreate ? ImColor(255, 255, 255, 255) : ImColor(150, 150, 150, 255),
            buttonIcon
        );
        ImGui::PopFont();

        // Draw text
        ImGui::GetWindowDrawList()->AddText(
            ImVec2(contentStartX + buttonIconSize.x + 8.0f, contentY),
            canCreate ? ImColor(255, 255, 255, 255) : ImColor(150, 150, 150, 255),
            buttonText
        );

        // Handle button click
        if (buttonClicked && canCreate) {
            popupState.isOperationInProgress = true;
            popupState.lastInteractionTime = ImGui::GetTime();

            try {
                g_ConfigSystem.shareConfigName = popupState.nameBuffer;
                g_ConfigSystem.shareConfigType = strlen(popupState.typeBuffer) > 0 ? popupState.typeBuffer : "Custom";

                bool success = false;
                if (popupState.isShareMode) {
                    success = ShareConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType);
                    if (success) {
                        g_NotificationManager.AddChange("Configurations", "Config Shared", false, true);
                    }
                } else {
                    success = CreateConfiguration(g_ConfigSystem.shareConfigName, g_ConfigSystem.shareConfigType);
                    if (success) {
                        g_NotificationManager.AddChange("Configurations", "Config Created", false, true);
                    }
                }

                if (success) {
                    popupState.shouldClose = true;
                } else {
                    popupState.hasError = true;
                    bool isDuplicate = false;
                    for (const auto& config : g_ConfigSystem.configs) {
                        if (config.name == g_ConfigSystem.shareConfigName) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    popupState.errorMessage = isDuplicate ?
                        "Configuration with this name already exists" :
                        (popupState.isShareMode ? "Failed to share configuration" : "Failed to create configuration file");
                }
            } catch (...) {
                popupState.hasError = true;
                popupState.errorMessage = popupState.isShareMode ?
                    "Unexpected error occurred while sharing configuration" :
                    "Unexpected error occurred while creating configuration";
            }

            popupState.isOperationInProgress = false;
        }

        // Move cursor past button
        ImGui::SetCursorScreenPos(ImVec2(buttonPos.x, buttonPos.y + buttonHeight + 10));

        // Handle popup closure
        if (popupState.shouldClose) {
            gui.picker_active = false;
            gui.darkoverlay = false;
            g_ConfigSystem.showSharePopup = false;
            popupState = ConfigPopupState(); // Reset state
            ImGui::CloseCurrentPopup();
            g_configPopupOpenFrame = -1;
        }

        ImGui::PopStyleVar(4);
        ImGui::PopStyleColor(2);
        ImGui::EndPopup();
    } else {
        // Popup was closed externally - reset immediately (no detection logic)
            gui.picker_active = false;
            gui.darkoverlay = false;
            g_ConfigSystem.showSharePopup = false;
            popupState = ConfigPopupState();
        g_configPopupOpenFrame = -1;
    }
}

void CGui::RenderProfileWindow() {
    // Always visible - completely isolated from menu animations
    const float profileAlpha = 1.0f; // Always fully visible regardless of menu state
    
    // Store current ImGui context state to isolate from animations
    ImGuiContext* ctx = ImGui::GetCurrentContext();
    float originalGlobalAlpha = ctx->Style.Alpha;
    
    // Save current style stack depth to detect any leaked style changes
    int originalStyleVarStackSize = ctx->StyleVarStack.Size;

    // Constants for styling - matching CheckboxComponent exactly
    const float component_height = 60.0f; // Same as CheckboxComponent
    const float brand_width = 80.0f;      // Width for brand image area
    const float content_padding = 15.0f;  // Internal padding
    const float element_spacing = 10.0f;  // Space between elements
    const float corner_radius = 3.0f;     // Same as CheckboxComponent

    // Calculate content dimensions
    const float username_width = 120.0f;
    const float expiry_width = 100.0f;
    const float fps_width = 80.0f;
    
    // Total window width = brand + content sections + padding
    const float total_width = brand_width + username_width + expiry_width + fps_width + (content_padding * 2) + (element_spacing * 3);

    // Position in top-right corner with proper margin from screen edge
    const float margin = 20.0f;
    ImVec2 screenSize = ImGui::GetIO().DisplaySize;
    ImVec2 windowPos = ImVec2(screenSize.x - total_width - margin, margin);

    // Set window properties - always on top and fixed position
    ImGui::SetNextWindowPos(windowPos, ImGuiCond_Always);
    ImGui::SetNextWindowSize(ImVec2(total_width, component_height), ImGuiCond_Always);

    // Window flags matching main menu style but always visible and non-interactive
    ImGuiWindowFlags window_flags = 
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoScrollbar |
        ImGuiWindowFlags_NoScrollWithMouse |
        ImGuiWindowFlags_NoCollapse |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_NoInputs |
        ImGuiWindowFlags_NoFocusOnAppearing |
        ImGuiWindowFlags_NoBringToFrontOnFocus;

    // Force isolated styling - completely ignore any global animation states
    ImGui::SetNextWindowBgAlpha(0.95f);
    
    // Temporarily override any global alpha changes from animations
    ctx->Style.Alpha = 1.0f;
    ImGui::PushStyleVar(ImGuiStyleVar_Alpha, 1.0f);

    if (ImGui::Begin("##ProfileWindow", nullptr, window_flags)) {
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowContentPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();

        // --- Brand/Logo Section - EXACTLY matching CheckboxComponent style ---
        // Animation state for accent colors only
        struct ProfileAnimState {
            ImVec4 rect_color = ImVec4(0, 0, 0, 0);
            ImVec4 icon_color = ImVec4(0, 0, 0, 0);
        };
        static ProfileAnimState profile_anim;
        const float anim_speed = GetAnimSpeed();
        
        // Use exact same accent colors as CheckboxComponent
        profile_anim.rect_color = ImLerp(profile_anim.rect_color,
            func.GetColorWithAlpha(gui.main, 0.8f), anim_speed);
        profile_anim.icon_color = ImLerp(profile_anim.icon_color,
            gui.main, anim_speed);

        // --- Brand/Logo Section (Left side) ---
        ImVec2 brandStart = windowContentPos;
        ImVec2 brandEnd = ImVec2(brandStart.x + brand_width, brandStart.y + component_height);

        // Brand background with shadow - same as CheckboxComponent left section
        //drawList->AddRectFilled(brandStart, brandEnd,
        //    ImGui::ColorConvertFloat4ToU32(profile_anim.rect_color), corner_radius, ImDrawFlags_RoundCornersLeft);

        //// Brand shadow effect - same as CheckboxComponent
        //drawList->AddShadowRect(brandStart,
        //    ImVec2(brandEnd.x - 5, brandEnd.y),
        //    ImGui::ColorConvertFloat4ToU32(profile_anim.rect_color), 20.0f, ImVec2(0, 0), ImDrawFlags_RoundCornersLeft, corner_radius);

        // Brand logo/icon in center of brand section
        ImGui::PushFont(iconsBig);
        const char* brandIcon = ICON_MS_SHIELD; // Or use a custom brand icon
        ImVec2 iconSize = ImGui::CalcTextSize(brandIcon);
        ImVec2 brandCenter = ImVec2(brandStart.x + brand_width / 2, brandStart.y + component_height / 2);
        ImVec2 iconPos = ImVec2(brandCenter.x - iconSize.x / 2, brandCenter.y - iconSize.y / 2);
        
        // Icon shadow - same as CheckboxComponent
        drawList->AddShadowCircle(brandCenter, 9.0f, ImGui::ColorConvertFloat4ToU32(profile_anim.icon_color), 40.0f, ImVec2(0, 0), 0, 360);
        
        // Draw icon
        drawList->AddText(iconPos, ImGui::ColorConvertFloat4ToU32(profile_anim.icon_color), brandIcon);
        ImGui::PopFont();

        // --- Content Sections ---
        float currentX = brandEnd.x + content_padding;
        const float textY = brandCenter.y - ImGui::CalcTextSize("A").y / 2; // Center text vertically

        // Helper to draw text section
        auto DrawTextSection = [&](const char* label, const char* value, float width, ImColor labelColor, ImColor valueColor) {
            ImVec2 sectionStart = ImVec2(currentX, windowContentPos.y);
            ImVec2 sectionEnd = ImVec2(currentX + width, windowContentPos.y + component_height);

            // Calculate text positions for perfect vertical centering
            ImVec2 labelSize = ImGui::CalcTextSize(label);
            ImVec2 valueSize = ImGui::CalcTextSize(value);
            
            float labelY = brandCenter.y - (labelSize.y + valueSize.y + 2) / 2; // 2px spacing between lines
            float valueY = labelY + labelSize.y + 2;

            // Center text horizontally in section
            float labelX = currentX + (width - labelSize.x) / 2;
            float valueX = currentX + (width - valueSize.x) / 2;

            // Draw label (smaller text)
            drawList->AddText(ImVec2(labelX, labelY), ImGui::ColorConvertFloat4ToU32(labelColor), label);
            
            // Draw value (main text)
            drawList->AddText(ImVec2(valueX, valueY), ImGui::ColorConvertFloat4ToU32(valueColor), value);

            currentX += width + element_spacing;
        };

        // --- Username Section ---
        // Get actual username (replace with real data source)
        const char* username = "User123"; // TODO: Replace with actual username from settings/config
        DrawTextSection("User", username, username_width, gui.text[1], gui.text[0]);

        // --- Expiration Section ---
        // Get actual expiration date (replace with real data source)
        const char* expiryDate = "30 Days"; // TODO: Replace with actual expiry calculation
        DrawTextSection("License", expiryDate, expiry_width, gui.text[1], gui.text[0]);

        // --- FPS Counter Section ---
        // Get actual FPS (replace with real FPS counter)
        static char fpsText[16];
        float currentFPS = ImGui::GetIO().Framerate;
        sprintf_s(fpsText, "%.0f", currentFPS);
        
        // Color FPS based on performance
        ImColor fpsColor = gui.text[0]; // Default
        if (currentFPS >= 60) fpsColor = ImColor(0, 255, 0, 255);      // Green for good FPS
        else if (currentFPS >= 30) fpsColor = ImColor(0, 255, 255, 255); // Yellow for medium FPS
        else fpsColor = ImColor(0, 0, 255, 255);                        // Red for low FPS

        DrawTextSection("FPS", fpsText, fps_width, gui.text[1], fpsColor);

        // --- Optional: Additional visual enhancements ---
        // Add subtle separator lines between sections (very faint)
        float separatorX = brandEnd.x + content_padding + username_width + element_spacing / 2;
        for (int i = 0; i < 2; i++) {
            ImVec2 lineStart = ImVec2(separatorX, windowContentPos.y + 15);
            ImVec2 lineEnd = ImVec2(separatorX, windowContentPos.y + component_height - 15);
            drawList->AddLine(lineStart, lineEnd, ImGui::ColorConvertFloat4ToU32(ImVec4(1.0f, 1.0f, 1.0f, 0.12f)), 1.0f);
            
            separatorX += (i == 0 ? expiry_width : fps_width) + element_spacing;
        }
    }
    ImGui::End();
    ImGui::PopStyleVar(1);  // Pop 1 style var: Alpha
    
    // Restore original global alpha to not affect other windows
    ctx->Style.Alpha = originalGlobalAlpha;
    
    // Check for leaked style variables from other systems and fix them
    if (ctx->StyleVarStack.Size != originalStyleVarStackSize) {
        // Other systems leaked style variables - clean them up
        while (ctx->StyleVarStack.Size > originalStyleVarStackSize) {
            ImGui::PopStyleVar();
        }
    }
}

void CGui::RenderHeaderWindow() {
    // Header window properties - same width as main menu
    const float headerHeight = 60.0f; // Same as CheckboxComponent height
    const float menuWidth = 1020.0f; // Same as main menu width
    const float margin = 0.0f; // No margin to align with main menu
    
    // Position directly above the main menu window
    ImVec2 headerPos = ImVec2(gui.window_pos.x, gui.window_pos.y - headerHeight - 5); // 5px gap
    
    // Set window properties
    ImGui::SetNextWindowPos(headerPos, ImGuiCond_Always);
    ImGui::SetNextWindowSize(ImVec2(menuWidth, headerHeight), ImGuiCond_Always);

    // Window flags - similar to main menu but fixed
    ImGuiWindowFlags window_flags = 
        ImGuiWindowFlags_NoTitleBar |
        ImGuiWindowFlags_NoResize |
        ImGuiWindowFlags_NoMove |
        ImGuiWindowFlags_NoScrollbar |
        ImGuiWindowFlags_NoScrollWithMouse |
        ImGuiWindowFlags_NoCollapse |
        ImGuiWindowFlags_NoSavedSettings |
        ImGuiWindowFlags_NoBringToFrontOnFocus;

    // Apply same transparency and animation state as main menu
    auto& headerBgState = AnimationSystem::g_AnimationManager.GetWindowState();
    if (headerBgState.isAnimating) {
        ImGui::SetNextWindowBgAlpha(headerBgState.alpha * 0.95f);
    } else {
        ImGui::SetNextWindowBgAlpha(0.95f);
    }

    ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 8.0f);
    ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(15, 15));

    if (ImGui::Begin("##HeaderWindow", nullptr, window_flags)) {
        // Apply content alpha animation only within this window context
        auto& headerWindowState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (headerWindowState.isAnimating) {
            ImGui::PushStyleVar(ImGuiStyleVar_Alpha, headerWindowState.alpha);
        }
        
        ImDrawList* drawList = ImGui::GetWindowDrawList();
        ImVec2 windowPos = ImGui::GetWindowPos();
        ImVec2 windowSize = ImGui::GetWindowSize();

        // Section widths
        const float leftSectionWidth = 250.0f;  // User section
        const float rightSectionWidth = 250.0f; // BITCHEATS section
        const float centerSectionWidth = windowSize.x - leftSectionWidth - rightSectionWidth;

        // Colors
        ImVec4 accentColor = ImVec4(0.0f, 0.58f, 1.0f, 1.0f);
        ImColor textColor = gui.text[0];
        ImColor subtleTextColor = gui.text[1];

        // === LEFT SECTION: User Logo and Name ===
        ImVec2 leftStart = ImVec2(windowPos.x + 15, windowPos.y + 10);
        
        // User logo (circle)
        float logoSize = 30.0f;
        ImVec2 logoCenter = ImVec2(leftStart.x + logoSize, leftStart.y + logoSize / 2 + 5);
        
        // Draw user circle with gradient
        drawList->AddCircleFilled(logoCenter, logoSize / 2, ImGui::ColorConvertFloat4ToU32(accentColor), 32);
        
        // Add user icon inside circle
        ImGui::PushFont(iconsBig);
        const char* userIcon = ICON_MS_PERSON;
        ImVec2 userIconSize = ImGui::CalcTextSize(userIcon);
        ImVec2 userIconPos = ImVec2(
            logoCenter.x - userIconSize.x / 2,
            logoCenter.y - userIconSize.y / 2
        );
        drawList->AddText(userIconPos, ImColor(255, 255, 255, 255), userIcon);
        ImGui::PopFont();
        
        // Username text next to logo
        const char* username = "Player"; // TODO: Replace with actual username
        ImVec2 usernamePos = ImVec2(leftStart.x + logoSize * 2 + 10, logoCenter.y - ImGui::CalcTextSize(username).y / 2);
        drawList->AddText(usernamePos, ImGui::ColorConvertFloat4ToU32(textColor), username);

        // === CENTER SECTION: Game Name ===
        const char* gameName = "Fortnite";
        ImGui::PushFont(iconsBig);
        ImVec2 gameNameSize = ImGui::CalcTextSize(gameName);
        ImGui::PopFont();
        
        ImVec2 gameNamePos = ImVec2(
            windowPos.x + leftSectionWidth + (centerSectionWidth - gameNameSize.x) / 2,
            windowPos.y + (headerHeight - gameNameSize.y) / 2
        );
        
        // Draw game name with glow effect
        ImGui::PushFont(iconsBig);
        drawList->AddText(gameNamePos, ImGui::ColorConvertFloat4ToU32(textColor), gameName);
        ImGui::PopFont();

        // === RIGHT SECTION: GIF Logo ===
        ImVec2 rightStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y);
        
        // Calculate display size
        float maxLogoWidth = rightSectionWidth - 20;
        float maxLogoHeight = headerHeight - 20;
        ImVec2 displaySize(maxLogoWidth, maxLogoHeight);

        // Display the animated GIF logo using HImGuiImageManager
        try {
            // Center the logo in the right section
            ImVec2 logoPos = ImVec2(
                rightStart.x + (rightSectionWidth - displaySize.x) / 2,
                rightStart.y + (headerHeight - displaySize.y) / 2
            );

            // Set cursor position for the GIF
            ImGui::SetCursorScreenPos(logoPos);

            //// Display the GIF with HImGuiImageManager
            //static bool gifLoaded = false;
            //static bool gifLoadAttempted = false;

            //if (!gifLoadAttempted) {
            //    gifLoadAttempted = true;
            //    HImage* gifImage = nullptr;
            //    if (HImageManager::GetImage_gif("C://Untitled-1.gif", gifImage, 1000.0f)) {
            //        gifLoaded = true;
            //    }
            //}

            //if (gifLoaded) {
            //    HImageManager::Image_gif("C://Untitled-1.gif", displaySize, 1000.0f);
            //} else {
            //    // Fallback to text if GIF loading failed
            //    const char* brandText = "BITCHEATS";
            //    ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
            //    ImVec2 brandTextPos = ImVec2(
            //        rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
            //        rightStart.y + (headerHeight - brandTextSize.y) / 2
            //    );
            //    drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
            //}
        } catch (...) {
            // Fallback to text if any error occurs
            const char* brandText = "BITCHEATS";
            ImVec2 brandTextSize = ImGui::CalcTextSize(brandText);
            ImVec2 brandTextPos = ImVec2(
                rightStart.x + (rightSectionWidth - brandTextSize.x) / 2,
                rightStart.y + (headerHeight - brandTextSize.y) / 2
            );
            drawList->AddText(brandTextPos, ImGui::ColorConvertFloat4ToU32(textColor), brandText);
        }

        // Update HImageManager
        HImageManager::updata(ImGui::GetIO().DeltaTime);

        // Optional: Add subtle separators between sections
        // Left separator
        ImVec2 leftSepStart = ImVec2(windowPos.x + leftSectionWidth, windowPos.y + 15);
        ImVec2 leftSepEnd = ImVec2(leftSepStart.x, windowPos.y + headerHeight - 15);
        drawList->AddLine(leftSepStart, leftSepEnd, ImColor(255, 255, 255, 30), 1.0f);
        
        // Right separator
        ImVec2 rightSepStart = ImVec2(windowPos.x + windowSize.x - rightSectionWidth, windowPos.y + 15);
        ImVec2 rightSepEnd = ImVec2(rightSepStart.x, windowPos.y + headerHeight - 15);
        drawList->AddLine(rightSepStart, rightSepEnd, ImColor(255, 255, 255, 30), 1.0f);
        
        // Pop content alpha animation if it was applied
        auto& headerContentState = AnimationSystem::g_AnimationManager.GetWindowState();
        if (headerContentState.isAnimating) {
            ImGui::PopStyleVar(); // Pop content alpha
        }
    }
    ImGui::End();
    ImGui::PopStyleVar(2); // Pop 2 style vars: WindowRounding, WindowPadding
}

// CGui Animation Integration Methods Implementation

void CGui::InitializeAnimations() {
    if (!animationsInitialized) {
        // Initialize HImageManager with DirectX texture callbacks
        auto createTexture = [](unsigned char* data, int width, int height, int channels) -> HTextureID {
            extern ID3D11Device* g_pd3dDevice;
            if (!g_pd3dDevice) return nullptr;

            ID3D11ShaderResourceView* srv = nullptr;
            D3D11_TEXTURE2D_DESC desc = {};
            desc.Width = width;
            desc.Height = height;
            desc.MipLevels = 1;
            desc.ArraySize = 1;
            desc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
            desc.SampleDesc.Count = 1;
            desc.Usage = D3D11_USAGE_DEFAULT;
            desc.BindFlags = D3D11_BIND_SHADER_RESOURCE;

            D3D11_SUBRESOURCE_DATA subResource = {};
            subResource.pSysMem = data;
            subResource.SysMemPitch = width * 4;

            ID3D11Texture2D* texture = nullptr;
            if (SUCCEEDED(g_pd3dDevice->CreateTexture2D(&desc, &subResource, &texture))) {
                D3D11_SHADER_RESOURCE_VIEW_DESC srvDesc = {};
                srvDesc.Format = desc.Format;
                srvDesc.ViewDimension = D3D11_SRV_DIMENSION_TEXTURE2D;
                srvDesc.Texture2D.MipLevels = 1;
                
                g_pd3dDevice->CreateShaderResourceView(texture, &srvDesc, &srv);
                texture->Release();
            }
            return srv;
        };

        auto deleteTexture = [](HTextureID texture) {
            if (texture) {
                ((ID3D11ShaderResourceView*)texture)->Release();
            }
        };

        // Set the function pointers
        HImageManager::GetIO().CreateTexture = static_cast<CreateTextureFunc>(createTexture);
        HImageManager::GetIO().DeleteTexture = static_cast<DeleteTextureFunc>(deleteTexture);

        AnimationSystem::g_AnimationManager.Initialize();
        animationsInitialized = true;
    }
}

void CGui::UpdateAnimations() {
    if (animationsInitialized) {
        AnimationSystem::g_AnimationManager.Update();
    }
}

void CGui::StartMenuShowAnimation() {
    isMenuAnimating = true;
    // Use smooth fade animation instead of bouncy scale for iOS-like feel
    AnimationSystem::g_AnimationManager.ShowWindow(0.2f, AnimationSystem::AnimationType::FadeIn);
    
    // Also start content fade in after a short delay
    StartContentFadeIn();
}

void CGui::StartMenuHideAnimation() {
    isMenuAnimating = true;
    // Use smooth fade out animation for iOS-like feel
    AnimationSystem::g_AnimationManager.HideWindow(0.15f, AnimationSystem::AnimationType::FadeOut);
}

void CGui::StartTabSwitchAnimation(int fromTab, int toTab) {
    isTabSwitching = true;
    AnimationSystem::g_AnimationManager.StartTabTransition(fromTab, toTab, 0.3f, AnimationSystem::AnimationType::SlideLeft);
    
    // Reset content animation for new tab
    AnimationSystem::g_AnimationManager.ResetContentAnimation();
    StartContentFadeIn();
}

void CGui::StartContentFadeIn() {
    // Estimate number of content elements (this will be refined per tab)
    contentElementCount = 15; // Average number of UI elements per tab
    AnimationSystem::g_AnimationManager.StartContentAnimation(contentElementCount, 0.05f, 0.6f);
}

void CGui::RenderAnimatedElement(int elementIndex, std::function<void()> renderFunction) {
    if (ShouldSkipElement(elementIndex)) {
        return;
    }
    
    ApplyElementAnimation(elementIndex);
    renderFunction();
}

void CGui::ShowAnimatedPopup(const char* popupId) {
    AnimationSystem::g_AnimationManager.ShowPopup(0.25f, AnimationSystem::AnimationType::ScaleIn);
    ImGui::OpenPopup(popupId);
}

void CGui::HideAnimatedPopup(const char* popupId) {
    AnimationSystem::g_AnimationManager.HidePopup(0.2f, AnimationSystem::AnimationType::ScaleOut);
}

bool CGui::BeginAnimatedWindow(const char* name, bool* p_open, ImGuiWindowFlags flags) {
    auto& animatedState = AnimationSystem::g_AnimationManager.GetWindowState();
    
    // Apply window-level alpha animation only to this specific window
    // Don't use PushStyleVar as it affects all subsequent windows
    if (animatedState.alpha < 1.0f) {
        ImGui::SetNextWindowBgAlpha(animatedState.alpha * 0.95f); // Apply to background
        // Note: We'll handle content alpha inside the window context
    } else {
        ImGui::SetNextWindowBgAlpha(0.95f); // Default transparency
    }
    
    return ImGui::Begin(name, p_open, flags);
}

void CGui::EndAnimatedWindow() {
    // No need to pop style vars since we're using SetNextWindowBgAlpha instead
    ImGui::End();
}

void CGui::BeginAnimatedContent() {
    // Reset element index for this frame
    featureIndex = 0;
}

void CGui::EndAnimatedContent() {
    // Content animation frame complete
}

bool CGui::ShouldSkipElement(int elementIndex) {
    // Always render during transitions to avoid flicker
    auto& tabState = AnimationSystem::g_AnimationManager.GetTabState();
    if (tabState.isTransitioning) {
        return false;
    }
    
    // Check if element should be rendered based on animation state
    return !AnimationSystem::g_AnimationManager.ShouldRenderElement(elementIndex);
}

void CGui::ApplyElementAnimation(int elementIndex) {
    float alpha = AnimationSystem::g_AnimationManager.GetElementAlpha(elementIndex);
    float slideOffset = AnimationSystem::g_AnimationManager.GetElementSlideOffset(elementIndex);
    float scale = AnimationSystem::g_AnimationManager.GetElementScale(elementIndex);
    
    // Note: Individual element alpha should be handled by the elements themselves
    // to avoid affecting other UI components. For now, we'll only handle slide offset.
    
    if (slideOffset != 0.0f) {
        ImVec2 currentPos = ImGui::GetCursorPos();
        ImGui::SetCursorPos(ImVec2(currentPos.x, currentPos.y + slideOffset));
    }
    
    // Scale can be applied to individual elements if needed
    // For now, we'll just use slide offset to avoid global state pollution
}
