﻿  nav_elements.cpp
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,20): error C2039: 'ColorGrid': is not a member of 'nav_elements'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.h(15): message : see declaration of 'nav_elements'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,103): error C2065: 'ColorState': undeclared identifier
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,96): error C2923: 'std::vector': 'ColorState' is not a valid template type argument for parameter '_Ty'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065): message : see declaration of 'ColorState'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,96): error C2976: 'std::vector': too few template arguments
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(430): message : see declaration of 'std::vector'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,127): error C2955: 'std::vector': use of class template requires template argument list
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(430): message : see declaration of 'std::vector'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4065,116): error C2955: 'std::vector': use of class template requires template argument list
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(430): message : see declaration of 'std::vector'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,46): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,46): message : Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,29): message : Conversion requires a second user-defined-conversion operator or constructor
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1534,49): message : see declaration of 'std::vector<_Ty,_Alloc>::size'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,87): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,87): message : Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4086,70): message : Conversion requires a second user-defined-conversion operator or constructor
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1534,49): message : see declaration of 'std::vector<_Ty,_Alloc>::size'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4119,45): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4119,45): message : Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4119,28): message : Conversion requires a second user-defined-conversion operator or constructor
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1534,49): message : see declaration of 'std::vector<_Ty,_Alloc>::size'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4203,45): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4203,45): message : Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4203,28): message : Conversion requires a second user-defined-conversion operator or constructor
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1534,49): message : see declaration of 'std::vector<_Ty,_Alloc>::size'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4209,42): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4209,42): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4209,67): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4209,67): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4210,26): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4210,26): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4210,51): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4210,51): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4230,12): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4230,12): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4269,16): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4269,16): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4270,48): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4270,48): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4273,30): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4273,30): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4273,61): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4273,61): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4274,30): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4274,30): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4274,61): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4274,61): message : while trying to match the argument list '(std::vector, size_t)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4292,8): error C2662: 'allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type std::vector<_Ty,_Alloc>::size(void) noexcept const': cannot convert 'this' pointer from 'std::vector' to 'const std::vector<_Ty,_Alloc> &'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4292,8): message : Reason: cannot convert from 'std::vector' to 'const std::vector<_Ty,_Alloc>'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4292,78): message : Conversion requires a second user-defined-conversion operator or constructor
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1534,49): message : see declaration of 'std::vector<_Ty,_Alloc>::size'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4325,80): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4325,80): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4326,80): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4326,80): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4327,80): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4327,80): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4328,80): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4328,80): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4341,55): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4341,55): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4342,55): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4342,55): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4343,55): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4343,55): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4344,55): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4344,55): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4347,20): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4347,20): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4348,59): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4348,59): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4349,59): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4349,59): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4350,59): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4350,59): message : while trying to match the argument list '(std::vector, int)'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4351,59): error C2678: binary '[': no operator found which takes a left-hand operand of type 'std::vector' (or there is no acceptable conversion)
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1559,50): message : could be 'const _Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept const'
C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Tools\MSVC\14.29.30133\include\vector(1549,44): message : or       '_Ty &std::vector<_Ty,_Alloc>::operator [](const allocator_traits<allocator_traits<_Alloc>::rebind_alloc<_Ty>>::size_type) noexcept'
C:\bitcheats_cheats - New UI\Menu UI\Components\nav_elements.cpp(4351,59): message : while trying to match the argument list '(std::vector, int)'
  GUI.cpp
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(1,1): warning C4005: 'IMGUI_DEFINE_MATH_OPERATORS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp : message : see previous definition of 'IMGUI_DEFINE_MATH_OPERATORS'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(21,1): warning C4005: 'RT_MANIFEST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(282): message : see previous definition of 'RT_MANIFEST'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(22,1): warning C4005: 'CREATEPROCESS_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(283): message : see previous definition of 'CREATEPROCESS_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(23,1): warning C4005: 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(284): message : see previous definition of 'ISOLATIONAWARE_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(24,1): warning C4005: 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(285): message : see previous definition of 'ISOLATIONAWARE_NOSTATICIMPORT_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(25,1): warning C4005: 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(286): message : see previous definition of 'ISOLATIONPOLICY_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(26,1): warning C4005: 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(287): message : see previous definition of 'ISOLATIONPOLICY_BROWSER_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(27,1): warning C4005: 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(288): message : see previous definition of 'MINIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.rh(28,1): warning C4005: 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID': macro redefinition
C:\Program Files (x86)\Windows Kits\10\Include\10.0.19041.0\um\winuser.h(289): message : see previous definition of 'MAXIMUM_RESERVED_MANIFEST_RESOURCE_ID'
C:\bitcheats_cheats - New UI\Cheat Core\Kernel Driver\Driver\Driver.h(51,3): warning C4091: 'typedef ': ignored on left of 'Addon::_lpReserved' when no variable is declared
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(906,1): warning C4005: 'ICON_MS_CROP_SQUARE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(12): message : see previous definition of 'ICON_MS_CROP_SQUARE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(929,1): warning C4005: 'ICON_MS_DANGEROUS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(13): message : see previous definition of 'ICON_MS_DANGEROUS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1039,1): warning C4005: 'ICON_MS_DIRECTIONS_CAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(14): message : see previous definition of 'ICON_MS_DIRECTIONS_CAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1446,1): warning C4005: 'ICON_MS_FOLDER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(15): message : see previous definition of 'ICON_MS_FOLDER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1513,1): warning C4005: 'ICON_MS_FORMAT_SIZE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(16): message : see previous definition of 'ICON_MS_FORMAT_SIZE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1568,1): warning C4005: 'ICON_MS_GAVEL': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(17): message : see previous definition of 'ICON_MS_GAVEL'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1673,1): warning C4005: 'ICON_MS_HEALING': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(18): message : see previous definition of 'ICON_MS_HEALING'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1810,1): warning C4005: 'ICON_MS_INFO': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(19): message : see previous definition of 'ICON_MS_INFO'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1842,1): warning C4005: 'ICON_MS_INVENTORY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(20): message : see previous definition of 'ICON_MS_INVENTORY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(1988,1): warning C4005: 'ICON_MS_LINE_WEIGHT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(21): message : see previous definition of 'ICON_MS_LINE_WEIGHT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2011,1): warning C4005: 'ICON_MS_LOCAL_DRINK': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(22): message : see previous definition of 'ICON_MS_LOCAL_DRINK'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2418,1): warning C4005: 'ICON_MS_NOTIFICATIONS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(23): message : see previous definition of 'ICON_MS_NOTIFICATIONS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2490,1): warning C4005: 'ICON_MS_PALETTE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(24): message : see previous definition of 'ICON_MS_PALETTE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2545,1): warning C4005: 'ICON_MS_PERSON': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(25): message : see previous definition of 'ICON_MS_PERSON'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2560,1): warning C4005: 'ICON_MS_PERSON_OFF': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(26): message : see previous definition of 'ICON_MS_PERSON_OFF'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2578,1): warning C4005: 'ICON_MS_PETS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(28): message : see previous definition of 'ICON_MS_PETS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2757,1): warning C4005: 'ICON_MS_RADAR': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(29): message : see previous definition of 'ICON_MS_RADAR'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2872,1): warning C4005: 'ICON_MS_ROCKET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(30): message : see previous definition of 'ICON_MS_ROCKET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2913,1): warning C4005: 'ICON_MS_SAVE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(31): message : see previous definition of 'ICON_MS_SAVE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(2929,1): warning C4005: 'ICON_MS_SCIENCE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(32): message : see previous definition of 'ICON_MS_SCIENCE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3017,1): warning C4005: 'ICON_MS_SETTINGS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(33): message : see previous definition of 'ICON_MS_SETTINGS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3067,1): warning C4005: 'ICON_MS_SHIELD': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(34): message : see previous definition of 'ICON_MS_SHIELD'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3162,1): warning C4005: 'ICON_MS_SMART_TOY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(35): message : see previous definition of 'ICON_MS_SMART_TOY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3211,1): warning C4005: 'ICON_MS_SPEED': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(36): message : see previous definition of 'ICON_MS_SPEED'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3250,1): warning C4005: 'ICON_MS_SPORTS_ESPORTS': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(37): message : see previous definition of 'ICON_MS_SPORTS_ESPORTS'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3331,1): warning C4005: 'ICON_MS_STRAIGHTEN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(39): message : see previous definition of 'ICON_MS_STRAIGHTEN'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3459,1): warning C4005: 'ICON_MS_TARGET': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(40): message : see previous definition of 'ICON_MS_TARGET'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3539,1): warning C4005: 'ICON_MS_TIMER': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(41): message : see previous definition of 'ICON_MS_TIMER'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3613,1): warning C4005: 'ICON_MS_TRANSLATE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(42): message : see previous definition of 'ICON_MS_TRANSLATE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3619,1): warning C4005: 'ICON_MS_TRENDING_FLAT': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(43): message : see previous definition of 'ICON_MS_TRENDING_FLAT'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3620,1): warning C4005: 'ICON_MS_TRENDING_UP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(44): message : see previous definition of 'ICON_MS_TRENDING_UP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3631,1): warning C4005: 'ICON_MS_TUNE': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(45): message : see previous definition of 'ICON_MS_TUNE'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3760,1): warning C4005: 'ICON_MS_VISIBILITY': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(46): message : see previous definition of 'ICON_MS_VISIBILITY'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3814,1): warning C4005: 'ICON_MS_WATER_DROP': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(48): message : see previous definition of 'ICON_MS_WATER_DROP'
C:\bitcheats_cheats - New UI\Menu UI\Fonts\font_defines.h(3916,1): warning C4005: 'ICON_MS_ZOOM_IN': macro redefinition
C:\bitcheats_cheats - New UI\Menu UI\Fonts\custom_icons.h(50): message : see previous definition of 'ICON_MS_ZOOM_IN'
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(237,46): warning C4838: conversion from 'int' to 'ImWchar' requires a narrowing conversion
C:\bitcheats_cheats - New UI\Menu UI\Framwork\GUI.cpp(2495,17): warning C4530: C++ exception handler used, but unwind semantics are not enabled. Specify /EHsc
